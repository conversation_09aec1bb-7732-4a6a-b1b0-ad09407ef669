"use client"

import { useState, useEffect } from "react"

export default function SoundToggle() {
  const [audioEnabled, setAudioEnabled] = useState(false)

  useEffect(() => {
    // Check saved audio preference
    const savedAudioEnabled = localStorage.getItem("audioEnabled")

    if (savedAudioEnabled === "true") {
      setAudioEnabled(true)

      // Play background audio with a delay
      const backgroundAudio = document.getElementById("background-sound") as HTMLAudioElement

      if (backgroundAudio) {
        backgroundAudio.volume = 0.05
        setTimeout(() => {
          backgroundAudio.play().catch((error) => console.log("Background audio failed:", error))
        }, 2000)
      }
    }

    // Initialize sound effects for interactive elements
    const initSoundEffects = () => {
      const interactiveElements = document.querySelectorAll("a, button, .service-card")
      const hoverSound = document.getElementById("hover-sound") as HTMLAudioElement
      const clickSound = document.getElementById("click-sound") as HTMLAudioElement

      interactiveElements.forEach((element) => {
        element.addEventListener("mouseenter", () => {
          if (audioEnabled && hoverSound) {
            hoverSound.volume = 0.1
            hoverSound.currentTime = 0
            hoverSound.play().catch((error) => {})
          }
        })

        element.addEventListener("click", () => {
          if (audioEnabled && clickSound) {
            clickSound.volume = 0.2
            clickSound.currentTime = 0
            clickSound.play().catch((error) => {})
          }
        })
      })
    }

    initSoundEffects()
  }, [audioEnabled])

  const toggleAudio = () => {
    const backgroundAudio = document.getElementById("background-sound") as HTMLAudioElement
    const clickSound = document.getElementById("click-sound") as HTMLAudioElement

    if (audioEnabled) {
      // Turn off audio
      setAudioEnabled(false)
      localStorage.setItem("audioEnabled", "false")

      if (backgroundAudio) {
        backgroundAudio.pause()
      }
    } else {
      // Turn on audio
      setAudioEnabled(true)
      localStorage.setItem("audioEnabled", "true")

      if (backgroundAudio) {
        backgroundAudio.volume = 0.05
        backgroundAudio.play().catch((error) => console.log("Background audio failed:", error))
      }
    }

    // Play click sound
    if (!audioEnabled && clickSound) {
      clickSound.currentTime = 0
      clickSound.play().catch((error) => {})
    }
  }

  return (
    <div className="sound-toggle" onClick={toggleAudio}>
      <div className={`sound-icon sound-on ${audioEnabled ? "active" : ""}`}>
        <i className="fas fa-volume-up"></i>
      </div>
      <div className={`sound-icon sound-off ${!audioEnabled ? "active" : ""}`}>
        <i className="fas fa-volume-mute"></i>
      </div>
    </div>
  )
}

