"use client"

import { useState, useEffect } from "react"

export default function BackToTop() {
  const [isVisible, setIsVisible] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    const handleScroll = () => {
      if (window.scrollY > 500) {
        setIsVisible(true)
      } else {
        setIsVisible(false)
      }
    }

    window.addEventListener("scroll", handleScroll)

    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    })

    // Play click sound if audio is enabled
    if (typeof window !== 'undefined') {
      const audioEnabled = localStorage.getItem("audioEnabled") === "true"
      const clickSound = document.getElementById("click-sound") as HTMLAudioElement

      if (audioEnabled && clickSound) {
        clickSound.currentTime = 0
        clickSound.play().catch((error) => {})
      }
    }
  }

  // Prevent hydration mismatch
  if (!isMounted) {
    return null
  }

  return (
    <div className={`back-to-top ${isVisible ? "visible" : ""}`} onClick={scrollToTop}>
      <i className="fas fa-chevron-up"></i>
    </div>
  )
}

