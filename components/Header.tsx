"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"

export default function Header() {
  const [isNavOpen, setIsNavOpen] = useState(false)
  const [isDarkTheme, setIsDarkTheme] = useState(true)
  const [isScrolled, setIsScrolled] = useState(false)
  const [isHidden, setIsHidden] = useState(false)
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null)
  const [isMounted, setIsMounted] = useState(false)
  const pathname = usePathname()
  const dropdownRef = useRef<HTMLLIElement>(null)

  let lastScrollY = 0

  useEffect(() => {
    setIsMounted(true)
    // Check saved theme preference
    const savedTheme = localStorage.getItem("theme")
    if (savedTheme === "light") {
      setIsDarkTheme(false)
      document.body.setAttribute("data-theme", "light")
    }

    // Click outside handler for dropdown
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveDropdown(null)
      }
    }

    const handleScroll = () => {
      const scrollY = window.scrollY

      // Header show/hide effect
      if (scrollY > 100) {
        setIsScrolled(true)

        if (scrollY > lastScrollY) {
          // Scrolling down
          setIsHidden(true)
        } else {
          // Scrolling up
          setIsHidden(false)
        }
      } else {
        setIsScrolled(false)
        setIsHidden(false)
      }

      lastScrollY = scrollY
    }

    // Add global event listeners
    window.addEventListener("scroll", handleScroll)
    document.addEventListener("mousedown", handleClickOutside)
    
    // Clean up
    return () => {
      window.removeEventListener("scroll", handleScroll)
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  const toggleNav = () => {
    setIsNavOpen(!isNavOpen)

    // Prevent scrolling when nav is open
    if (!isNavOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = ""
    }
  }

  const toggleTheme = () => {
    if (isDarkTheme) {
      document.body.setAttribute("data-theme", "light")
      localStorage.setItem("theme", "light")
    } else {
      document.body.removeAttribute("data-theme")
      localStorage.setItem("theme", "dark")
    }

    setIsDarkTheme(!isDarkTheme)
  }

  const toggleDropdown = (index: number, event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()
    setActiveDropdown(activeDropdown === index ? null : index)
  }

  const closeNav = () => {
    setIsNavOpen(false)
    setActiveDropdown(null)
    document.body.style.overflow = ""
  }

  // Handle dropdown link click
  const handleDropdownClick = () => {
    // Force close all menus
    closeNav()
  }

  // Prevent hydration mismatch
  if (!isMounted) {
    return (
      <header>
        <div className="header-container">
          <div className="logo-container">
            <Link href="/">
              <img src="/assets/logo.png" alt="Qoverse Logo" className="logo-img" />
            </Link>
          </div>
          <div className="nav-toggle">
            <div className="hamburger">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          <nav>
            <ul className="nav-links">
              <li>
                <Link href="/" className="nav-link">Home</Link>
              </li>
              <li className="dropdown">
                <div className="nav-link-wrapper">
                  <Link href="/services" className="nav-link">Services</Link>
                  <span className="dropdown-toggle">
                    <i className="fas fa-chevron-down"></i>
                  </span>
                </div>
              </li>
              <li>
                <Link href="/about" className="nav-link">About</Link>
              </li>
              <li>
                <Link href="/contact" className="nav-link">Contact</Link>
              </li>
            </ul>
          </nav>
          <div className="theme-toggle">
            <i className="fas fa-moon"></i>
          </div>
        </div>
      </header>
    )
  }

  return (
    <header className={`${isScrolled ? "solid" : ""} ${isHidden ? "hidden" : ""}`}>
      <div className="header-container">
        <div className="logo-container">
          <Link href="/" onClick={closeNav}>
            <img src="/assets/logo.png" alt="Qoverse Logo" className="logo-img" />
          </Link>
        </div>

        <div className={`nav-toggle ${isNavOpen ? "active" : ""}`} onClick={toggleNav}>
          <div className="hamburger">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>

        <nav className={isNavOpen ? "active" : ""}>
          <ul className="nav-links">
            <li>
              <Link href="/" className={`nav-link ${pathname === "/" ? "active" : ""}`} onClick={closeNav}>
                Home
              </Link>
            </li>
            <li ref={dropdownRef} className={`dropdown ${activeDropdown === 0 ? "active" : ""}`}>
              <div className="nav-link-wrapper">
                <Link
                  href="/services"
                  className={`nav-link ${pathname === "/services" ? "active" : ""}`}
                  onClick={(e) => {
                    if (window.innerWidth < 768) {
                      closeNav()
                    } else {
                      toggleDropdown(0, e)
                    }
                  }}
                >
                  Services
                </Link>
                <span 
                  className="dropdown-toggle" 
                  onClick={(e) => toggleDropdown(0, e)}
                >
                  <i className="fas fa-chevron-down"></i>
                </span>
              </div>
              <div className="dropdown-wrapper" onClick={(e) => e.stopPropagation()}>
                <ul className="dropdown-content">
                  <li>
                    <Link href="/services#generative-ai" className="dropdown-link" onClick={handleDropdownClick}>
                      <span>01</span> Generative AI
                    </Link>
                  </li>
                  <li>
                    <Link href="/services#web-mobile" className="dropdown-link" onClick={handleDropdownClick}>
                      <span>02</span> Web & Mobile Development
                    </Link>
                  </li>
                  <li>
                    <Link href="/services#data-management" className="dropdown-link" onClick={handleDropdownClick}>
                      <span>03</span> Data & Analytics
                    </Link>
                  </li>
                  <li>
                    <Link href="/services#digital-commerce" className="dropdown-link" onClick={handleDropdownClick}>
                      <span>04</span> Digital Commerce
                    </Link>
                  </li>
                  <li>
                    <Link href="/services#ai-bi" className="dropdown-link" onClick={handleDropdownClick}>
                      <span>05</span> AI Business Intelligence
                    </Link>
                  </li>
                  <li>
                    <Link href="/services#cloud-native" className="dropdown-link" onClick={handleDropdownClick}>
                      <span>06</span> Cloud-Native Solutions
                    </Link>
                  </li>
                  <li>
                    <Link href="/services#product-design" className="dropdown-link" onClick={handleDropdownClick}>
                      <span>07</span> Product Design & UI/UX
                    </Link>
                  </li>
                  <li>
                    <Link href="/services#custom-software" className="dropdown-link" onClick={handleDropdownClick}>
                      <span>08</span> Custom Software
                    </Link>
                  </li>
                  <li>
                    <Link href="/services#devops" className="dropdown-link" onClick={handleDropdownClick}>
                      <span>09</span> DevOps & Automation
                    </Link>
                  </li>
                  <li>
                    <Link href="/services#consulting" className="dropdown-link" onClick={handleDropdownClick}>
                      <span>10</span> Technology Consulting
                    </Link>
                  </li>
                </ul>
              </div>
            </li>
            <li>
              <Link href="/about" className={`nav-link ${pathname === "/about" ? "active" : ""}`} onClick={closeNav}>
                About Us
              </Link>
            </li>
            <li>
              <Link
                href="/contact"
                className={`nav-link ${pathname === "/contact" ? "active" : ""}`}
                onClick={closeNav}
              >
                Contact
              </Link>
            </li>
          </ul>
        </nav>

        <div className="theme-toggle" onClick={toggleTheme}>
          <span className={`theme-icon light-icon ${!isDarkTheme ? "active" : ""}`}>☀️</span>
          <span className={`theme-icon dark-icon ${isDarkTheme ? "active" : ""}`}>🌙</span>
        </div>
      </div>
    </header>
  )
}