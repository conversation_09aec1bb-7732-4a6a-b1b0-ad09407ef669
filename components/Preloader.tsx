"use client"

import { useEffect, useState } from "react"

export default function Preloader() {
  const [isVisible, setIsVisible] = useState(true)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    // Hide preloader after a delay
    const timer = setTimeout(() => {
      setIsVisible(false)
    }, 1500)

    return () => clearTimeout(timer)
  }, [])

  // Prevent hydration mismatch by not rendering until mounted
  if (!isMounted) {
    return (
      <div id="preloader">
        <div className="loader">
          <svg viewBox="0 0 80 80">
            <circle id="loader-circle" cx="40" cy="40" r="32"></circle>
          </svg>
          <div className="loader-text">QOVERSE</div>
        </div>
      </div>
    )
  }

  if (!isVisible) return null

  return (
    <div id="preloader">
      <div className="loader">
        <svg viewBox="0 0 80 80">
          <circle id="loader-circle" cx="40" cy="40" r="32"></circle>
        </svg>
        <div className="loader-text">QOVERSE</div>
      </div>
    </div>
  )
}

