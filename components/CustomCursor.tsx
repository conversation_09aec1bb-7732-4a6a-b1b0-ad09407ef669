"use client"

import { useEffect, useState } from "react"

export default function CustomCursor() {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // Check if device is mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)

    if (!isMobile) {
      const cursorDot = document.querySelector(".cursor-dot") as HTMLElement
      const cursorOutline = document.querySelector(".cursor-outline") as HTMLElement

      if (cursorDot && cursorOutline) {
        // Show custom cursor after a delay
        setTimeout(() => {
          cursorDot.style.opacity = "1"
          cursorOutline.style.opacity = "1"
        }, 1000)

        // Update cursor position on mouse move
        const handleMouseMove = (e: MouseEvent) => {
          cursorDot.style.left = `${e.clientX}px`
          cursorDot.style.top = `${e.clientY}px`

          // Add slight delay for cursor outline for smoother effect
          setTimeout(() => {
            cursorOutline.style.left = `${e.clientX}px`
            cursorOutline.style.top = `${e.clientY}px`
          }, 50)
        }

        window.addEventListener("mousemove", handleMouseMove)

        // Cursor interactions with interactive elements
        const cursorElements = document.querySelectorAll(
          "a, button, .service-card, .nav-toggle, .theme-toggle, .sound-toggle",
        )

        cursorElements.forEach((element) => {
          element.addEventListener("mouseenter", () => {
            cursorOutline.style.width = "50px"
            cursorOutline.style.height = "50px"
            cursorOutline.style.borderColor = "var(--accent-color)"
          })

          element.addEventListener("mouseleave", () => {
            cursorOutline.style.width = "40px"
            cursorOutline.style.height = "40px"
            cursorOutline.style.borderColor = "rgba(65, 105, 225, 0.5)"
          })
        })

        return () => {
          window.removeEventListener("mousemove", handleMouseMove)
        }
      }
    }

    return () => {
      window.removeEventListener("resize", checkMobile)
    }
  }, [isMobile])

  if (isMobile) return null

  return (
    <>
      <div className="cursor-dot"></div>
      <div className="cursor-outline"></div>
    </>
  )
}

