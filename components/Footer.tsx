// import Link from "next/link"

// export default function Footer() {
//   return (
//     <footer>
//       <div className="footer-main">
//         <div className="container">
//           <div className="footer-grid">
//             <div className="footer-info">
//               <img src="/assets/logo.png" alt="Qoverse Logo" className="footer-logo" />
//               <p className="footer-tagline">
//                 Creating innovative software solutions that power digital transformation.
//               </p>
//               <div className="footer-social">
//                 <a
//                   href="https://www.linkedin.com/company/qoverse"
//                   target="_blank"
//                   rel="noopener noreferrer"
//                   className="social-link"
//                 >
//                   <i className="fab fa-linkedin"></i>
//                 </a>
//                 <a href="https://www.x.com/qoverse" target="_blank" rel="noopener noreferrer" className="social-link">
//                   <i className="fab fa-x-twitter"></i>
//                 </a>
//               </div>
//             </div>
//             <div className="footer-nav">
//               <h4>Quick Links</h4>
//               <ul>
//                 <li>
//                   <Link href="/">Home</Link>
//                 </li>
//                 <li>
//                   <Link href="/services">Services</Link>
//                 </li>
//                 <li>
//                   <Link href="/about">About Us</Link>
//                 </li>
//                 <li>
//                   <Link href="/contact">Contact</Link>
//                 </li>
//               </ul>
//             </div>
//             <div className="footer-services">
//               <h4>Our Services</h4>
//               <ul>
//                 <li>
//                   <Link href="/services#generative-ai">Generative AI</Link>
//                 </li>
//                 <li>
//                   <Link href="/services#web-mobile">Web & Mobile Development</Link>
//                 </li>
//                 <li>
//                   <Link href="/services#data-management">Data & Analytics</Link>
//                 </li>
//                 <li>
//                   <Link href="/services#digital-commerce">Digital Commerce</Link>
//                 </li>
//                 <li>
//                   <Link href="/services#ai-bi">AI Business Intelligence</Link>
//                 </li>
//                 <li>
//                   <Link href="/services#cloud-native">Cloud-Native Solutions</Link>
//                 </li>
//               </ul>
//             </div>
//             <div className="footer-contact">
//               <h4>Contact Us</h4>
//               <div className="contact-item">
//                 <i className="fas fa-map-marker-alt"></i>
//                 <p>Estonia, Harju maakond, Kuusalu vald, Pudisoo küla, Männimäe/1, 74626</p>
//               </div>
//               <div className="contact-item">
//                 <i className="fas fa-phone-alt"></i>
//                 <p>
//                   <a href="tel:+***********">+****************</a>
//                 </p>
//               </div>
//               <div className="contact-item">
//                 <i className="fas fa-envelope"></i>
//                 <p>
//                   <a href="mailto:<EMAIL>"><EMAIL></a>
//                 </p>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//       <div className="footer-bottom">
//         <div className="container">
//           <p>&copy; {new Date().getFullYear()} Qoverse. All rights reserved.</p>
//           <div className="footer-links">
//             {/* <a href="privacy.html">Privacy Policy</a> */}
//             {/* <a href="terms.html">Terms of Service</a> */}
//           </div>
//         </div>
//       </div>
//     </footer>
//   )
// }

import Link from "next/link"

export default function Footer() {
  return (
    <footer>
      <div className="footer-main">
        <div className="container">
          <div className="footer-grid">
            <div className="footer-info">
              <img src="/assets/logo.png" alt="Qoverse Logo" className="footer-logo" />
              <p className="footer-tagline">
                Creating innovative software solutions that power digital transformation.
              </p>
              {/* <div className="footer-social">
                <a
                  href="https://www.linkedin.com/company/qoverse"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link"
                >
                  <i className="fab fa-linkedin"></i>
                </a>
                <a href="https://www.x.com/qoverse" target="_blank" rel="noopener noreferrer" className="social-link">
                  <i className="fab fa-x-twitter"></i>
                </a> */}
            <div className="social-links">
              <a
                href="https://www.linkedin.com/company/qoverse"
                target="_blank"
                rel="noopener noreferrer"
                className="social-link"
              >
                <i className="fab fa-linkedin"></i>
              </a>
              <a 
                href="https://www.x.com/qoverse" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="social-link"
              >
                <i className="fab fa-twitter"></i>
              </a>
              </div>
            </div>
            <div className="footer-nav">
              <h4>Quick Links</h4>
              <ul>
                <li>
                  <Link href="/">Home</Link>
                </li>
                <li>
                  <Link href="/services">Services</Link>
                </li>
                <li>
                  <Link href="/about">About Us</Link>
                </li>
                <li>
                  <Link href="/contact">Contact</Link>
                </li>
              </ul>
            </div>
            <div className="footer-services">
              <h4>Our Services</h4>
              <ul>
                <li>
                  <Link href="/services#generative-ai">Generative AI</Link>
                </li>
                <li>
                  <Link href="/services#web-mobile">Web & Mobile Development</Link>
                </li>
                <li>
                  <Link href="/services#data-management">Data & Analytics</Link>
                </li>
                <li>
                  <Link href="/services#digital-commerce">Digital Commerce</Link>
                </li>
                <li>
                  <Link href="/services#ai-bi">AI Business Intelligence</Link>
                </li>
                <li>
                  <Link href="/services#cloud-native">Cloud-Native Solutions</Link>
                </li>
              </ul>
            </div>
            <div className="footer-contact">
              <h4>Contact Us</h4>
              <div className="contact-item">
                <i className="fas fa-map-marker-alt"></i>
                <p>Estonia, Harju maakond, Kuusalu vald, Pudisoo küla, Männimäe/1, 74626</p>
              </div>
              {/* <div className="contact-item">
                <i className="fas fa-phone-alt"></i>
                <p>
                  <a href="tel:+***********">+****************</a>
                </p>
              </div> */}
              <div className="contact-item">
                <i className="fas fa-envelope"></i>
                <p>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="footer-bottom">
        <div className="container">
          <p>&copy; {new Date().getFullYear()} Qoverse. All rights reserved.</p>
          <div className="footer-links">
            <Link href="/privacy">Privacy Policy</Link>
            <Link href="/terms">Terms of Service</Link>
          </div>
        </div>
      </div>
    </footer>
  )
}