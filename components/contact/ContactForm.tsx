"use client";
import { useState, ChangeEvent, FormEvent } from 'react';

interface ValidationErrors {
  [key: string]: string;
}

export default function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: '',
    message: ''
  });
  const [formStatus, setFormStatus] = useState({
    message: '',
    type: ''
  });
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear validation error when user types
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = {...prev};
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const errors: ValidationErrors = {};
    
    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        errors.email = 'Please enter a valid email address';
      }
    }
    
    if (!formData.message.trim()) {
      errors.message = 'Message is required';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setFormStatus({
      message: 'Sending your message...',
      type: 'info'
    });
    
    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      const result = await response.json();
      
      if (response.ok) {
        setFormStatus({
          message: result.message || 'Message sent successfully!',
          type: 'success'
        });
        setFormData({
          name: '',
          email: '',
          phone: '',
          service: '',
          message: ''
        });
      } else {
        setFormStatus({
          message: result.error || 'Failed to send message. Please try again.',
          type: 'error'
        });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setFormStatus({
        message: 'Network error. Please try again later.',
        type: 'error'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="contact-form-container">
      <form className="contact-form" onSubmit={handleSubmit}>
        <div className="form-header">
          <h3>Send Us a Message</h3>
          <p>Fill out the form below and we'll get back to you as soon as possible.</p>
        </div>
        
        <div className={`form-group ${validationErrors.name ? 'error' : ''}`}>
          <input 
            type="text" 
            id="name" 
            name="name" 
            value={formData.name}
            onChange={handleChange}
            placeholder="Your Name" 
            required 
          />
          <div className="form-validation">{validationErrors.name}</div>
        </div>
        
        <div className={`form-group ${validationErrors.email ? 'error' : ''}`}>
          <input 
            type="email" 
            id="email" 
            name="email" 
            value={formData.email}
            onChange={handleChange}
            placeholder="Your Email" 
            required 
          />
          <div className="form-validation">{validationErrors.email}</div>
        </div>
        
        <div className="form-group">
          <input 
            type="tel" 
            id="phone" 
            name="phone" 
            value={formData.phone}
            onChange={handleChange}
            placeholder="Your Phone" 
          />
          <div className="form-validation"></div>
        </div>
        
        <div className="form-group">
          <select 
            id="service" 
            name="service"
            value={formData.service}
            onChange={handleChange}
          >
            <option value="" disabled>Select Service</option>
            <option value="Generative AI">Generative AI</option>
            <option value="Web & Mobile Development">Web & Mobile Development</option>
            <option value="Data & Analytics">Data & Analytics</option>
            <option value="Digital Commerce">Digital Commerce</option>
            <option value="AI Business Intelligence">AI Business Intelligence</option>
            <option value="Cloud-Native Solutions">Cloud-Native Solutions</option>
            <option value="Product Design & UI/UX">Product Design & UI/UX</option>
            <option value="Custom Software">Custom Software</option>
            <option value="DevOps & Automation">DevOps & Automation</option>
            <option value="Technology Consulting">Technology Consulting</option>
            <option value="Other">Other</option>
          </select>
          <div className="form-validation"></div>
        </div>
        
        <div className={`form-group ${validationErrors.message ? 'error' : ''}`}>
          <textarea 
            id="message" 
            name="message" 
            value={formData.message}
            onChange={handleChange}
            placeholder="Your Message" 
            required
          ></textarea>
          <div className="form-validation">{validationErrors.message}</div>
        </div>
        
        <button 
          type="submit" 
          className="cta-button primary form-submit"
          disabled={isSubmitting}
        >
          <span className="button-text">
            {isSubmitting ? 'Sending...' : 'Send Message'}
          </span>
          {!isSubmitting && <span className="button-icon">→</span>}
        </button>
        
        <div className={`form-status ${formStatus.type}`}>
          {formStatus.message}
        </div>
      </form>
    </div>
  );
}