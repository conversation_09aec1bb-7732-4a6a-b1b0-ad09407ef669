"use client";
import { useState, useEffect } from 'react';

interface FAQItem {
  id: number;
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    id: 1,
    question: "What types of businesses do you work with?",
    answer: "We work with businesses of all sizes, from startups to enterprise organizations, across various industries including healthcare, finance, e-commerce, education, manufacturing, and more. Our solutions are tailored to meet the specific needs of each client."
  },
  {
    id: 2,
    question: "How long does a typical project take to complete?",
    answer: "Project timelines vary depending on scope, complexity, and requirements. Small projects may take 2-4 weeks, while more complex enterprise solutions can take several months. During our initial consultation, we'll provide a detailed timeline based on your specific project needs."
  },
  {
    id: 3,
    question: "What is your development process?",
    answer: "We follow an agile development methodology that includes: 1) Discovery and planning, 2) Design and prototyping, 3) Development with regular iterations, 4) Testing and quality assurance, 5) Deployment, and 6) Ongoing support and maintenance. This approach ensures transparency, flexibility, and quality throughout the project lifecycle."
  },
  {
    id: 4,
    question: "How do you handle project management and communication?",
    answer: "We assign a dedicated project manager to each client who serves as your main point of contact. We use collaboration tools like <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and regular video meetings to ensure clear communication. You'll receive regular updates on progress, milestones, and any challenges that arise during development."
  },
  {
    id: 5,
    question: "Do you provide ongoing support after project completion?",
    answer: "Yes, we offer various support and maintenance packages to ensure your solution continues to perform optimally. These include technical support, bug fixes, security updates, feature enhancements, and performance optimization. We can tailor a support plan to meet your specific needs and budget."
  }
];

export default function FAQ() {
  const [activeItems, setActiveItems] = useState<number[]>([]);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    // Make first FAQ item active by default
    setTimeout(() => {
      setActiveItems([1]);
    }, 1000);
  }, []);

  const handleFaqClick = (id: number) => {
    setActiveItems(prev => {
      if (prev.includes(id)) {
        return prev.filter(item => item !== id);
      } else {
        return [id]; // Only allow one item to be active at a time
      }
    });

    // Play sound effect if enabled
    if (typeof window !== 'undefined') {
      const clickSound = document.getElementById('click-sound') as HTMLAudioElement;
      if (clickSound && (window as any).audioEnabled) {
        clickSound.currentTime = 0;
        clickSound.play().catch(e => {});
      }
    }
  };

  // Prevent hydration mismatch
  if (!isMounted) {
    return (
      <section id="faq" className="section">
        <div className="container">
          <div className="section-header">
            <span className="section-subtitle">Questions & Answers</span>
            <h2 className="section-title">Frequently Asked Questions</h2>
            <p className="section-description">Find answers to common questions about our services and process.</p>
          </div>
          <div className="faq-container">
            {faqData.map((item) => (
              <div key={item.id} className="faq-item">
                <div className="faq-question">
                  <h3>{item.question}</h3>
                  <div className="question-icon">
                    <i className="fas fa-plus"></i>
                  </div>
                </div>
                <div className="faq-answer">
                  <p>{item.answer}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="faq" className="section">
      <div className="container">
        <div className="section-header">
          <span className="section-subtitle">Questions & Answers</span>
          <h2 className="section-title">Frequently Asked Questions</h2>
          <p className="section-description">Find answers to common questions about our services and process.</p>
        </div>
        <div className="faq-container">
          {faqData.map((item) => (
            <div key={item.id} className={`faq-item ${activeItems.includes(item.id) ? 'active' : ''}`}>
              <div className="faq-question" onClick={() => handleFaqClick(item.id)}>
                <h3>{item.question}</h3>
                <div className="question-icon">
                  <i className="fas fa-plus"></i>
                </div>
              </div>
              <div className="faq-answer">
                <p>{item.answer}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}