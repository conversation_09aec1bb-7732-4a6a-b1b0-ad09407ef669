"use client";
import { useEffect } from 'react';

export default function FAQ() {
  useEffect(() => {
    const faqItems = document.querySelectorAll('.faq-item');
    
    const handleFaqClick = (item: HTMLElement) => {
      // Close all other items
      faqItems.forEach(otherItem => {
        if (otherItem !== item && otherItem.classList.contains('active')) {
          otherItem.classList.remove('active');
        }
      });
      
      // Toggle current item
      item.classList.toggle('active');
      
      // Play sound effect if enabled
      const clickSound = document.getElementById('click-sound') as HTMLAudioElement;
      if (clickSound && window.audioEnabled) {
        clickSound.currentTime = 0;
        clickSound.play().catch(e => {});
      }
    };
    
    faqItems.forEach(item => {
      const question = item.querySelector('.faq-question');
      if (question) {
        question.addEventListener('click', () => handleFaqClick(item as HTMLElement));
      }
    });
    
    // Make first FAQ item active by default
    if (faqItems.length > 0) {
      setTimeout(() => {
        faqItems[0].classList.add('active');
      }, 1000);
    }
    
    // Cleanup
    return () => {
      faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        if (question) {
          question.removeEventListener('click', () => handleFaqClick(item as HTMLElement));
        }
      });
    };
  }, []);

  return (
    <section id="faq" className="section">
      <div className="container">
        <div className="section-header">
          <span className="section-subtitle">Questions & Answers</span>
          <h2 className="section-title">Frequently Asked Questions</h2>
          <p className="section-description">Find answers to common questions about our services and process.</p>
        </div>
        <div className="faq-container">
          <div className="faq-item">
            <div className="faq-question">
              <h3>What types of businesses do you work with?</h3>
              <div className="question-icon">
                <i className="fas fa-plus"></i>
              </div>
            </div>
            <div className="faq-answer">
              <p>We work with businesses of all sizes, from startups to enterprise organizations, across various industries including healthcare, finance, e-commerce, education, manufacturing, and more. Our solutions are tailored to meet the specific needs of each client.</p>
            </div>
          </div>
          <div className="faq-item">
            <div className="faq-question">
              <h3>How long does a typical project take to complete?</h3>
              <div className="question-icon">
                <i className="fas fa-plus"></i>
              </div>
            </div>
            <div className="faq-answer">
              <p>Project timelines vary depending on scope, complexity, and requirements. Small projects may take 2-4 weeks, while more complex enterprise solutions can take several months. During our initial consultation, we'll provide a detailed timeline based on your specific project needs.</p>
            </div>
          </div>
          <div className="faq-item">
            <div className="faq-question">
              <h3>What is your development process?</h3>
              <div className="question-icon">
                <i className="fas fa-plus"></i>
              </div>
            </div>
            <div className="faq-answer">
              <p>We follow an agile development methodology that includes: 1) Discovery and planning, 2) Design and prototyping, 3) Development with regular iterations, 4) Testing and quality assurance, 5) Deployment, and 6) Ongoing support and maintenance. This approach ensures transparency, flexibility, and quality throughout the project lifecycle.</p>
            </div>
          </div>
          <div className="faq-item">
            <div className="faq-question">
              <h3>How do you handle project management and communication?</h3>
              <div className="question-icon">
                <i className="fas fa-plus"></i>
              </div>
            </div>
            <div className="faq-answer">
              <p>We assign a dedicated project manager to each client who serves as your main point of contact. We use collaboration tools like Jira, Slack, and regular video meetings to ensure clear communication. You'll receive regular updates on progress, milestones, and any challenges that arise during development.</p>
            </div>
          </div>
          <div className="faq-item">
            <div className="faq-question">
              <h3>Do you provide ongoing support after project completion?</h3>
              <div className="question-icon">
                <i className="fas fa-plus"></i>
              </div>
            </div>
            <div className="faq-answer">
              <p>Yes, we offer various support and maintenance packages to ensure your solution continues to perform optimally. These include technical support, bug fixes, security updates, feature enhancements, and performance optimization. We can tailor a support plan to meet your specific needs and budget.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}