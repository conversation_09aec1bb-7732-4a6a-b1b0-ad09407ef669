"use client";
import { useState, useEffect } from 'react';

interface Pattern {
  id: number;
  sequence: number[];
  nextValue: number;
  difficulty: 'easy' | 'medium' | 'hard';
  type: 'arithmetic' | 'geometric' | 'fibonacci' | 'prime' | 'custom';
}

const patterns: Pattern[] = [
  {
    id: 1,
    sequence: [2, 4, 6, 8, 10],
    nextValue: 12,
    difficulty: 'easy',
    type: 'arithmetic'
  },
  {
    id: 2,
    sequence: [1, 1, 2, 3, 5],
    nextValue: 8,
    difficulty: 'medium',
    type: 'fibonacci'
  },
  {
    id: 3,
    sequence: [2, 6, 18, 54],
    nextValue: 162,
    difficulty: 'medium',
    type: 'geometric'
  },
  {
    id: 4,
    sequence: [2, 3, 5, 7, 11],
    nextValue: 13,
    difficulty: 'hard',
    type: 'prime'
  },
  {
    id: 5,
    sequence: [1, 4, 9, 16, 25],
    nextValue: 36,
    difficulty: 'easy',
    type: 'custom'
  },
  {
    id: 6,
    sequence: [1, 8, 27, 64],
    nextValue: 125,
    difficulty: 'medium',
    type: 'custom'
  }
];

export default function PatternRecognition() {
  const [currentPattern, setCurrentPattern] = useState(0);
  const [userAnswer, setUserAnswer] = useState('');
  const [score, setScore] = useState(0);
  const [attempts, setAttempts] = useState(0);
  const [feedback, setFeedback] = useState('');
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [aiAnalysis, setAiAnalysis] = useState('');
  const [showHint, setShowHint] = useState(false);

  const getPatternDescription = (pattern: Pattern) => {
    switch (pattern.type) {
      case 'arithmetic':
        return 'Arithmetic sequence - each number increases by a constant difference';
      case 'geometric':
        return 'Geometric sequence - each number is multiplied by a constant ratio';
      case 'fibonacci':
        return 'Fibonacci sequence - each number is the sum of the two preceding ones';
      case 'prime':
        return 'Prime numbers - numbers divisible only by 1 and themselves';
      case 'custom':
        if (pattern.sequence.every((n, i) => n === (i + 1) ** 2)) {
          return 'Perfect squares - squares of consecutive integers';
        } else if (pattern.sequence.every((n, i) => n === (i + 1) ** 3)) {
          return 'Perfect cubes - cubes of consecutive integers';
        }
        return 'Custom mathematical pattern';
      default:
        return 'Mathematical sequence';
    }
  };

  const analyzePattern = (pattern: Pattern) => {
    const seq = pattern.sequence;
    let analysis = "AI Analysis: ";
    
    switch (pattern.type) {
      case 'arithmetic':
        const diff = seq[1] - seq[0];
        analysis += `This is an arithmetic progression with common difference ${diff}. Next term = ${seq[seq.length - 1]} + ${diff} = ${pattern.nextValue}`;
        break;
      case 'geometric':
        const ratio = seq[1] / seq[0];
        analysis += `This is a geometric progression with common ratio ${ratio}. Next term = ${seq[seq.length - 1]} × ${ratio} = ${pattern.nextValue}`;
        break;
      case 'fibonacci':
        analysis += `Fibonacci sequence where each term is the sum of the previous two: ${seq[seq.length - 2]} + ${seq[seq.length - 1]} = ${pattern.nextValue}`;
        break;
      case 'prime':
        analysis += `Prime number sequence. The next prime number after ${seq[seq.length - 1]} is ${pattern.nextValue}`;
        break;
      case 'custom':
        if (pattern.sequence.every((n, i) => n === (i + 1) ** 2)) {
          analysis += `Perfect squares: 1², 2², 3², 4², 5²... Next is 6² = ${pattern.nextValue}`;
        } else if (pattern.sequence.every((n, i) => n === (i + 1) ** 3)) {
          analysis += `Perfect cubes: 1³, 2³, 3³, 4³... Next is 5³ = ${pattern.nextValue}`;
        }
        break;
    }
    
    return analysis;
  };

  const checkAnswer = () => {
    const answer = parseInt(userAnswer);
    const correct = answer === patterns[currentPattern].nextValue;
    
    setIsCorrect(correct);
    setAttempts(prev => prev + 1);
    
    if (correct) {
      setScore(prev => prev + 1);
      setFeedback('🎉 Correct! Your pattern recognition skills are impressive!');
    } else {
      setFeedback(`❌ Not quite. The correct answer is ${patterns[currentPattern].nextValue}`);
    }
    
    setAiAnalysis(analyzePattern(patterns[currentPattern]));
  };

  const nextPattern = () => {
    setCurrentPattern(prev => (prev + 1) % patterns.length);
    setUserAnswer('');
    setFeedback('');
    setIsCorrect(null);
    setAiAnalysis('');
    setShowHint(false);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '#10b981';
      case 'medium': return '#f59e0b';
      case 'hard': return '#ef4444';
      default: return '#6366f1';
    }
  };

  return (
    <div className="pattern-recognition">
      <div className="game-header">
        <h3>AI Pattern Recognition Challenge</h3>
        <p>Test your intelligence against AI pattern analysis</p>
        <div className="score-board">
          <div className="score-item">
            <span className="score-label">Score:</span>
            <span className="score-value">{score}/{attempts}</span>
          </div>
          <div className="score-item">
            <span className="score-label">Accuracy:</span>
            <span className="score-value">
              {attempts > 0 ? Math.round((score / attempts) * 100) : 0}%
            </span>
          </div>
        </div>
      </div>

      <div className="pattern-container">
        <div className="difficulty-badge" style={{ backgroundColor: getDifficultyColor(patterns[currentPattern].difficulty) }}>
          {patterns[currentPattern].difficulty.toUpperCase()}
        </div>
        
        <div className="sequence-display">
          <h4>Find the next number in this sequence:</h4>
          <div className="sequence">
            {patterns[currentPattern].sequence.map((num, index) => (
              <div key={index} className="sequence-number">
                {num}
              </div>
            ))}
            <div className="sequence-question">?</div>
          </div>
        </div>

        <div className="input-section">
          <input
            type="number"
            value={userAnswer}
            onChange={(e) => setUserAnswer(e.target.value)}
            placeholder="Enter your answer"
            className="answer-input"
            disabled={isCorrect !== null}
          />
          <button
            onClick={checkAnswer}
            disabled={!userAnswer || isCorrect !== null}
            className="check-btn"
          >
            <i className="fas fa-brain"></i>
            Analyze Pattern
          </button>
        </div>

        {feedback && (
          <div className={`feedback ${isCorrect ? 'correct' : 'incorrect'}`}>
            {feedback}
          </div>
        )}

        {aiAnalysis && (
          <div className="ai-analysis">
            <div className="analysis-header">
              <i className="fas fa-robot"></i>
              <span>AI Pattern Analysis</span>
            </div>
            <p>{aiAnalysis}</p>
          </div>
        )}

        <div className="game-controls">
          <button
            onClick={() => setShowHint(!showHint)}
            className="hint-btn"
            disabled={isCorrect !== null}
          >
            <i className="fas fa-lightbulb"></i>
            {showHint ? 'Hide Hint' : 'Show Hint'}
          </button>
          
          <button onClick={nextPattern} className="next-btn">
            <i className="fas fa-forward"></i>
            Next Challenge
          </button>
        </div>

        {showHint && (
          <div className="hint-section">
            <div className="hint-header">
              <i className="fas fa-info-circle"></i>
              <span>Pattern Hint</span>
            </div>
            <p>{getPatternDescription(patterns[currentPattern])}</p>
          </div>
        )}
      </div>

      <style jsx>{`
        .pattern-recognition {
          background: var(--surface-card);
          backdrop-filter: blur(20px);
          border-radius: var(--border-radius-lg);
          padding: 3rem;
          border: 1px solid var(--border-color);
          margin: 2rem 0;
        }

        .game-header {
          text-align: center;
          margin-bottom: 3rem;
        }

        .game-header h3 {
          font-size: 2.4rem;
          margin-bottom: 1rem;
          background: var(--gradient-primary);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .score-board {
          display: flex;
          justify-content: center;
          gap: 3rem;
          margin-top: 2rem;
        }

        .score-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
        }

        .score-label {
          font-size: 1.2rem;
          color: var(--text-secondary);
        }

        .score-value {
          font-size: 2rem;
          font-weight: 700;
          color: var(--accent-color);
        }

        .pattern-container {
          position: relative;
        }

        .difficulty-badge {
          position: absolute;
          top: -1rem;
          right: -1rem;
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 20px;
          font-size: 1.2rem;
          font-weight: 600;
          box-shadow: var(--shadow-md);
        }

        .sequence-display {
          text-align: center;
          margin-bottom: 3rem;
        }

        .sequence-display h4 {
          margin-bottom: 2rem;
          color: var(--text-primary);
        }

        .sequence {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 1.5rem;
          flex-wrap: wrap;
        }

        .sequence-number {
          background: var(--gradient-primary);
          color: white;
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.8rem;
          font-weight: 700;
          box-shadow: var(--shadow-glow);
          animation: pulse-number 2s ease-in-out infinite;
        }

        .sequence-question {
          background: var(--tertiary-bg);
          border: 2px dashed var(--accent-color);
          color: var(--accent-color);
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 2rem;
          font-weight: 700;
          animation: question-pulse 1.5s ease-in-out infinite;
        }

        @keyframes pulse-number {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }

        @keyframes question-pulse {
          0%, 100% { opacity: 0.7; }
          50% { opacity: 1; }
        }

        .input-section {
          display: flex;
          gap: 1.5rem;
          justify-content: center;
          margin-bottom: 2rem;
        }

        .answer-input {
          padding: 1.2rem 1.5rem;
          border-radius: var(--border-radius);
          border: 2px solid var(--border-color);
          background: var(--tertiary-bg);
          color: var(--text-primary);
          font-size: 1.6rem;
          text-align: center;
          width: 150px;
          transition: all 0.3s ease;
        }

        .answer-input:focus {
          outline: none;
          border-color: var(--accent-color);
          box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.2);
        }

        .check-btn {
          background: var(--gradient-primary);
          color: white;
          border: none;
          padding: 1.2rem 2rem;
          border-radius: var(--border-radius);
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 0.8rem;
        }

        .check-btn:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: var(--shadow-glow);
        }

        .check-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .feedback {
          text-align: center;
          padding: 1.5rem;
          border-radius: var(--border-radius);
          margin-bottom: 2rem;
          font-size: 1.6rem;
          font-weight: 600;
        }

        .feedback.correct {
          background: rgba(16, 185, 129, 0.1);
          border: 1px solid #10b981;
          color: #10b981;
        }

        .feedback.incorrect {
          background: rgba(239, 68, 68, 0.1);
          border: 1px solid #ef4444;
          color: #ef4444;
        }

        .ai-analysis {
          background: var(--surface-glass);
          border-radius: var(--border-radius);
          padding: 2rem;
          margin-bottom: 2rem;
          border: 1px solid var(--border-secondary);
        }

        .analysis-header, .hint-header {
          display: flex;
          align-items: center;
          gap: 1rem;
          margin-bottom: 1rem;
          color: var(--accent-secondary);
          font-weight: 600;
        }

        .game-controls {
          display: flex;
          justify-content: center;
          gap: 1.5rem;
          margin-bottom: 2rem;
        }

        .hint-btn, .next-btn {
          padding: 1rem 2rem;
          border-radius: var(--border-radius);
          border: 1px solid var(--border-color);
          background: var(--surface-glass);
          color: var(--text-primary);
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 0.8rem;
        }

        .hint-btn:hover, .next-btn:hover {
          background: var(--accent-color);
          color: white;
          transform: translateY(-2px);
        }

        .hint-section {
          background: rgba(245, 158, 11, 0.1);
          border: 1px solid #f59e0b;
          border-radius: var(--border-radius);
          padding: 2rem;
        }

        @media (max-width: 768px) {
          .sequence {
            gap: 1rem;
          }
          
          .sequence-number, .sequence-question {
            width: 50px;
            height: 50px;
            font-size: 1.4rem;
          }
          
          .input-section {
            flex-direction: column;
            align-items: center;
          }
          
          .answer-input {
            width: 200px;
          }
          
          .game-controls {
            flex-direction: column;
            align-items: center;
          }
        }
      `}</style>
    </div>
  );
}
