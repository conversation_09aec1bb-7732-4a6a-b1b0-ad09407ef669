"use client";
import { useState, useEffect } from 'react';

interface CodeExample {
  language: string;
  prompt: string;
  code: string;
  explanation: string;
}

const codeExamples: CodeExample[] = [
  {
    language: 'Python',
    prompt: 'Create a machine learning model for image classification',
    code: `import tensorflow as tf
from tensorflow.keras import layers, models

# Create CNN model for image classification
model = models.Sequential([
    layers.Conv2D(32, (3, 3), activation='relu', input_shape=(224, 224, 3)),
    layers.MaxPooling2D((2, 2)),
    layers.Conv2D(64, (3, 3), activation='relu'),
    layers.MaxPooling2D((2, 2)),
    layers.Conv2D(64, (3, 3), activation='relu'),
    layers.Flatten(),
    layers.Dense(64, activation='relu'),
    layers.Dense(10, activation='softmax')
])

# Compile the model
model.compile(optimizer='adam',
              loss='sparse_categorical_crossentropy',
              metrics=['accuracy'])

print("AI Model created successfully!")`,
    explanation: 'This creates a Convolutional Neural Network for image classification with multiple layers for feature extraction and pattern recognition.'
  },
  {
    language: 'JavaScript',
    prompt: 'Build a neural network from scratch',
    code: `class NeuralNetwork {
    constructor(inputNodes, hiddenNodes, outputNodes) {
        this.inputNodes = inputNodes;
        this.hiddenNodes = hiddenNodes;
        this.outputNodes = outputNodes;
        
        // Initialize weights with random values
        this.weightsIH = this.randomMatrix(hiddenNodes, inputNodes);
        this.weightsHO = this.randomMatrix(outputNodes, hiddenNodes);
        
        this.learningRate = 0.1;
    }
    
    predict(inputArray) {
        // Forward propagation
        let inputs = Matrix.fromArray(inputArray);
        let hidden = Matrix.multiply(this.weightsIH, inputs);
        hidden.map(this.sigmoid);
        
        let outputs = Matrix.multiply(this.weightsHO, hidden);
        outputs.map(this.sigmoid);
        
        return outputs.toArray();
    }
    
    sigmoid(x) {
        return 1 / (1 + Math.exp(-x));
    }
}`,
    explanation: 'A complete neural network implementation with forward propagation, weight initialization, and sigmoid activation function.'
  },
  {
    language: 'Python',
    prompt: 'Create an AI chatbot with natural language processing',
    code: `import nltk
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

class AIChatbot:
    def __init__(self):
        self.responses = [
            "Hello! How can I help you today?",
            "I'm an AI assistant powered by machine learning.",
            "That's an interesting question! Let me think...",
            "I use natural language processing to understand you.",
            "My neural networks are processing your request."
        ]
        self.vectorizer = TfidfVectorizer()
        
    def preprocess_text(self, text):
        # Tokenize and clean text
        tokens = nltk.word_tokenize(text.lower())
        return ' '.join(tokens)
    
    def generate_response(self, user_input):
        # Process input with NLP
        processed_input = self.preprocess_text(user_input)
        
        # Use AI to find best response
        all_texts = self.responses + [processed_input]
        tfidf_matrix = self.vectorizer.fit_transform(all_texts)
        
        # Calculate similarity scores
        similarity_scores = cosine_similarity(tfidf_matrix[-1], tfidf_matrix[:-1])
        best_match_idx = np.argmax(similarity_scores)
        
        return self.responses[best_match_idx]

# Initialize AI chatbot
chatbot = AIChatbot()
response = chatbot.generate_response("Hello, how are you?")
print(f"AI: {response}")`,
    explanation: 'An intelligent chatbot using NLP techniques, TF-IDF vectorization, and cosine similarity for response generation.'
  }
];

export default function AICodeGenerator() {
  const [currentExample, setCurrentExample] = useState(0);
  const [displayedCode, setDisplayedCode] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [typingIndex, setTypingIndex] = useState(0);

  useEffect(() => {
    if (isGenerating && typingIndex < codeExamples[currentExample].code.length) {
      const timer = setTimeout(() => {
        setDisplayedCode(prev => prev + codeExamples[currentExample].code[typingIndex]);
        setTypingIndex(prev => prev + 1);
      }, 30);
      return () => clearTimeout(timer);
    } else if (typingIndex >= codeExamples[currentExample].code.length) {
      setIsGenerating(false);
    }
  }, [isGenerating, typingIndex, currentExample]);

  const generateCode = () => {
    setIsGenerating(true);
    setDisplayedCode('');
    setTypingIndex(0);
  };

  const nextExample = () => {
    setCurrentExample((prev) => (prev + 1) % codeExamples.length);
    setDisplayedCode('');
    setTypingIndex(0);
    setIsGenerating(false);
  };

  const copyCode = () => {
    navigator.clipboard.writeText(codeExamples[currentExample].code);
  };

  return (
    <div className="ai-code-generator">
      <div className="generator-header">
        <h3>AI Code Generator</h3>
        <p>Watch AI generate intelligent code solutions in real-time</p>
      </div>

      <div className="prompt-section">
        <div className="prompt-label">
          <i className="fas fa-robot"></i>
          <span>AI Prompt:</span>
        </div>
        <div className="prompt-text">
          {codeExamples[currentExample].prompt}
        </div>
      </div>

      <div className="code-output">
        <div className="code-header">
          <div className="language-tag">
            {codeExamples[currentExample].language}
          </div>
          <div className="code-actions">
            <button onClick={copyCode} className="action-btn" title="Copy Code">
              <i className="fas fa-copy"></i>
            </button>
            <button onClick={nextExample} className="action-btn" title="Next Example">
              <i className="fas fa-forward"></i>
            </button>
          </div>
        </div>
        
        <div className="code-content">
          <pre>
            <code>
              {displayedCode}
              {isGenerating && <span className="cursor">|</span>}
            </code>
          </pre>
        </div>
      </div>

      <div className="explanation-section">
        <h4>AI Explanation:</h4>
        <p>{codeExamples[currentExample].explanation}</p>
      </div>

      <div className="generator-controls">
        <button
          onClick={generateCode}
          disabled={isGenerating}
          className={`generate-btn ${isGenerating ? 'generating' : ''}`}
        >
          {isGenerating ? (
            <>
              <i className="fas fa-cog fa-spin"></i>
              Generating Code...
            </>
          ) : (
            <>
              <i className="fas fa-magic"></i>
              Generate AI Code
            </>
          )}
        </button>
      </div>

      <style jsx>{`
        .ai-code-generator {
          background: var(--surface-card);
          backdrop-filter: blur(20px);
          border-radius: var(--border-radius-lg);
          padding: 3rem;
          border: 1px solid var(--border-color);
          margin: 2rem 0;
        }

        .generator-header {
          text-align: center;
          margin-bottom: 3rem;
        }

        .generator-header h3 {
          font-size: 2.4rem;
          margin-bottom: 1rem;
          background: var(--gradient-primary);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .prompt-section {
          background: var(--tertiary-bg);
          border-radius: var(--border-radius);
          padding: 2rem;
          margin-bottom: 2rem;
          border-left: 4px solid var(--accent-color);
        }

        .prompt-label {
          display: flex;
          align-items: center;
          gap: 1rem;
          font-weight: 600;
          color: var(--accent-color);
          margin-bottom: 1rem;
        }

        .prompt-text {
          font-size: 1.6rem;
          color: var(--text-primary);
          font-style: italic;
        }

        .code-output {
          background: var(--primary-bg);
          border-radius: var(--border-radius);
          border: 1px solid var(--border-color);
          margin-bottom: 2rem;
          overflow: hidden;
        }

        .code-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem 1.5rem;
          background: var(--tertiary-bg);
          border-bottom: 1px solid var(--border-color);
        }

        .language-tag {
          background: var(--gradient-primary);
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 20px;
          font-size: 1.2rem;
          font-weight: 600;
        }

        .code-actions {
          display: flex;
          gap: 1rem;
        }

        .action-btn {
          background: var(--surface-glass);
          border: 1px solid var(--border-color);
          color: var(--text-primary);
          padding: 0.5rem 1rem;
          border-radius: var(--border-radius);
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .action-btn:hover {
          background: var(--accent-color);
          color: white;
          transform: scale(1.1);
        }

        .code-content {
          padding: 2rem;
          font-family: var(--font-mono);
          font-size: 1.4rem;
          line-height: 1.6;
          color: var(--text-primary);
          min-height: 300px;
          overflow-x: auto;
        }

        .cursor {
          animation: blink 1s infinite;
          color: var(--accent-color);
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }

        .explanation-section {
          background: var(--surface-glass);
          border-radius: var(--border-radius);
          padding: 2rem;
          margin-bottom: 2rem;
          border: 1px solid var(--border-secondary);
        }

        .explanation-section h4 {
          color: var(--accent-secondary);
          margin-bottom: 1rem;
          font-size: 1.8rem;
        }

        .generator-controls {
          text-align: center;
        }

        .generate-btn {
          background: var(--gradient-primary);
          color: white;
          border: none;
          padding: 1.5rem 3rem;
          border-radius: 50px;
          font-size: 1.6rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.4s ease;
          box-shadow: var(--shadow-glow);
          display: inline-flex;
          align-items: center;
          gap: 1rem;
        }

        .generate-btn:hover:not(:disabled) {
          transform: translateY(-3px) scale(1.05);
          box-shadow: 0 15px 35px rgba(0, 212, 255, 0.5);
        }

        .generate-btn:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .generate-btn.generating {
          background: var(--gradient-neural);
        }

        @media (max-width: 768px) {
          .ai-code-generator {
            padding: 2rem;
          }
          
          .code-content {
            font-size: 1.2rem;
            padding: 1.5rem;
          }
          
          .generate-btn {
            width: 100%;
            padding: 1.2rem 2rem;
          }
        }
      `}</style>
    </div>
  );
}
