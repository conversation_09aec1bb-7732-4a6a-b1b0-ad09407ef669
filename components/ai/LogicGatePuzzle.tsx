"use client";
import { useState, useEffect } from 'react';

interface LogicGate {
  id: string;
  type: 'AND' | 'OR' | 'NOT' | 'XOR' | 'NAND' | 'NOR';
  inputs: boolean[];
  output: boolean;
  x: number;
  y: number;
}

interface PuzzleLevel {
  id: number;
  title: string;
  description: string;
  targetOutput: boolean[];
  inputs: boolean[];
  availableGates: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

const puzzleLevels: PuzzleLevel[] = [
  {
    id: 1,
    title: "Basic AND Gate",
    description: "Create a circuit that outputs TRUE only when both inputs are TRUE",
    targetOutput: [false, false, false, true],
    inputs: [false, false, true, true],
    availableGates: ['AND'],
    difficulty: 'beginner'
  },
  {
    id: 2,
    title: "OR Logic Challenge",
    description: "Build a circuit that outputs TRUE when at least one input is TRUE",
    targetOutput: [false, true, true, true],
    inputs: [false, false, true, true],
    availableGates: ['OR'],
    difficulty: 'beginner'
  },
  {
    id: 3,
    title: "XOR Puzzle",
    description: "Create an exclusive OR - TRUE when inputs are different",
    targetOutput: [false, true, true, false],
    inputs: [false, false, true, true],
    availableGates: ['XOR'],
    difficulty: 'intermediate'
  },
  {
    id: 4,
    title: "Complex Logic",
    description: "Combine multiple gates to match the target pattern",
    targetOutput: [true, false, false, true],
    inputs: [false, false, true, true],
    availableGates: ['AND', 'OR', 'NOT'],
    difficulty: 'advanced'
  }
];

export default function LogicGatePuzzle() {
  const [currentLevel, setCurrentLevel] = useState(0);
  const [selectedGate, setSelectedGate] = useState<string | null>(null);
  const [circuit, setCircuit] = useState<LogicGate[]>([]);
  const [userOutput, setUserOutput] = useState<boolean[]>([]);
  const [isCorrect, setIsCorrect] = useState(false);
  const [showSolution, setShowSolution] = useState(false);

  const calculateGateOutput = (gate: LogicGate): boolean => {
    const [a, b] = gate.inputs;
    switch (gate.type) {
      case 'AND': return a && b;
      case 'OR': return a || b;
      case 'NOT': return !a;
      case 'XOR': return a !== b;
      case 'NAND': return !(a && b);
      case 'NOR': return !(a || b);
      default: return false;
    }
  };

  const simulateCircuit = () => {
    const level = puzzleLevels[currentLevel];
    const results: boolean[] = [];
    
    // Test all input combinations
    for (let i = 0; i < level.inputs.length; i += 2) {
      const inputA = level.inputs[i];
      const inputB = level.inputs[i + 1];
      
      // For now, simulate with the first gate in circuit
      if (circuit.length > 0) {
        const gate = { ...circuit[0], inputs: [inputA, inputB] };
        results.push(calculateGateOutput(gate));
      } else {
        results.push(false);
      }
    }
    
    setUserOutput(results);
    setIsCorrect(JSON.stringify(results) === JSON.stringify(level.targetOutput));
  };

  const addGate = (gateType: string) => {
    const newGate: LogicGate = {
      id: `gate-${Date.now()}`,
      type: gateType as LogicGate['type'],
      inputs: [false, false],
      output: false,
      x: 200,
      y: 150
    };
    
    setCircuit([newGate]); // For simplicity, only one gate for now
    setSelectedGate(null);
  };

  const nextLevel = () => {
    if (currentLevel < puzzleLevels.length - 1) {
      setCurrentLevel(prev => prev + 1);
      setCircuit([]);
      setUserOutput([]);
      setIsCorrect(false);
      setShowSolution(false);
    }
  };

  const resetLevel = () => {
    setCircuit([]);
    setUserOutput([]);
    setIsCorrect(false);
    setShowSolution(false);
  };

  useEffect(() => {
    if (circuit.length > 0) {
      simulateCircuit();
    }
  }, [circuit, currentLevel]);

  const level = puzzleLevels[currentLevel];

  return (
    <div className="logic-gate-puzzle">
      <div className="puzzle-header">
        <h3>AI Logic Gate Puzzle</h3>
        <p>Master digital logic - the foundation of AI computing</p>
        <div className="level-info">
          <span className="level-badge">Level {level.id}</span>
          <span className={`difficulty-badge ${level.difficulty}`}>
            {level.difficulty.toUpperCase()}
          </span>
        </div>
      </div>

      <div className="puzzle-content">
        <div className="challenge-description">
          <h4>{level.title}</h4>
          <p>{level.description}</p>
        </div>

        <div className="truth-table">
          <h5>Target Truth Table:</h5>
          <table>
            <thead>
              <tr>
                <th>Input A</th>
                <th>Input B</th>
                <th>Target</th>
                <th>Your Output</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {level.targetOutput.map((target, index) => (
                <tr key={index}>
                  <td className={level.inputs[index * 2] ? 'true' : 'false'}>
                    {level.inputs[index * 2] ? '1' : '0'}
                  </td>
                  <td className={level.inputs[index * 2 + 1] ? 'true' : 'false'}>
                    {level.inputs[index * 2 + 1] ? '1' : '0'}
                  </td>
                  <td className={target ? 'true' : 'false'}>
                    {target ? '1' : '0'}
                  </td>
                  <td className={userOutput[index] ? 'true' : 'false'}>
                    {userOutput.length > index ? (userOutput[index] ? '1' : '0') : '-'}
                  </td>
                  <td>
                    {userOutput.length > index ? (
                      userOutput[index] === target ? '✅' : '❌'
                    ) : '⏳'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="gate-selector">
          <h5>Available Logic Gates:</h5>
          <div className="gate-buttons">
            {level.availableGates.map(gateType => (
              <button
                key={gateType}
                onClick={() => addGate(gateType)}
                className={`gate-btn ${selectedGate === gateType ? 'selected' : ''}`}
              >
                <div className="gate-symbol">{gateType}</div>
                <div className="gate-description">
                  {getGateDescription(gateType)}
                </div>
              </button>
            ))}
          </div>
        </div>

        <div className="circuit-area">
          <h5>Your Circuit:</h5>
          <div className="circuit-canvas">
            {circuit.length === 0 ? (
              <div className="empty-circuit">
                <i className="fas fa-microchip"></i>
                <p>Select a logic gate to build your circuit</p>
              </div>
            ) : (
              <div className="circuit-display">
                {circuit.map(gate => (
                  <div key={gate.id} className="gate-component">
                    <div className="gate-inputs">
                      <div className="input-pin">A</div>
                      <div className="input-pin">B</div>
                    </div>
                    <div className="gate-body">
                      {gate.type}
                    </div>
                    <div className="gate-output">
                      <div className="output-pin">OUT</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {isCorrect && (
          <div className="success-message">
            <i className="fas fa-trophy"></i>
            <h4>Puzzle Solved!</h4>
            <p>Excellent! You've mastered this logic pattern.</p>
          </div>
        )}

        <div className="puzzle-controls">
          <button onClick={resetLevel} className="control-btn reset">
            <i className="fas fa-redo"></i>
            Reset
          </button>
          
          <button 
            onClick={() => setShowSolution(!showSolution)} 
            className="control-btn hint"
          >
            <i className="fas fa-lightbulb"></i>
            {showSolution ? 'Hide' : 'Show'} Solution
          </button>
          
          <button 
            onClick={nextLevel} 
            disabled={!isCorrect || currentLevel >= puzzleLevels.length - 1}
            className="control-btn next"
          >
            <i className="fas fa-arrow-right"></i>
            Next Level
          </button>
        </div>

        {showSolution && (
          <div className="solution-hint">
            <h5>💡 Solution Hint:</h5>
            <p>Use the <strong>{level.availableGates[0]}</strong> gate to achieve the target pattern.</p>
            <p>Remember: {getGateDescription(level.availableGates[0])}</p>
          </div>
        )}
      </div>

      <style jsx>{`
        .logic-gate-puzzle {
          background: var(--surface-card);
          backdrop-filter: blur(20px);
          border-radius: var(--border-radius-lg);
          padding: 3rem;
          border: 1px solid var(--border-color);
          margin: 2rem 0;
        }

        .puzzle-header {
          text-align: center;
          margin-bottom: 3rem;
        }

        .puzzle-header h3 {
          font-size: 2.4rem;
          margin-bottom: 1rem;
          background: var(--gradient-primary);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .level-info {
          display: flex;
          justify-content: center;
          gap: 1rem;
          margin-top: 1rem;
        }

        .level-badge {
          background: var(--gradient-primary);
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 20px;
          font-weight: 600;
        }

        .difficulty-badge {
          padding: 0.5rem 1rem;
          border-radius: 20px;
          font-weight: 600;
          color: white;
        }

        .difficulty-badge.beginner { background: #10b981; }
        .difficulty-badge.intermediate { background: #f59e0b; }
        .difficulty-badge.advanced { background: #ef4444; }

        .challenge-description {
          background: var(--tertiary-bg);
          padding: 2rem;
          border-radius: var(--border-radius);
          margin-bottom: 2rem;
          border-left: 4px solid var(--accent-color);
        }

        .truth-table {
          margin-bottom: 2rem;
        }

        .truth-table table {
          width: 100%;
          border-collapse: collapse;
          background: var(--primary-bg);
          border-radius: var(--border-radius);
          overflow: hidden;
        }

        .truth-table th, .truth-table td {
          padding: 1rem;
          text-align: center;
          border-bottom: 1px solid var(--border-color);
        }

        .truth-table th {
          background: var(--tertiary-bg);
          font-weight: 600;
          color: var(--text-primary);
        }

        .truth-table td.true {
          background: rgba(16, 185, 129, 0.2);
          color: #10b981;
          font-weight: 600;
        }

        .truth-table td.false {
          background: rgba(239, 68, 68, 0.2);
          color: #ef4444;
          font-weight: 600;
        }

        .gate-selector {
          margin-bottom: 2rem;
        }

        .gate-buttons {
          display: flex;
          gap: 1rem;
          flex-wrap: wrap;
          justify-content: center;
        }

        .gate-btn {
          background: var(--surface-glass);
          border: 2px solid var(--border-color);
          border-radius: var(--border-radius);
          padding: 1.5rem;
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: center;
          min-width: 120px;
        }

        .gate-btn:hover, .gate-btn.selected {
          border-color: var(--accent-color);
          background: rgba(0, 212, 255, 0.1);
          transform: translateY(-2px);
        }

        .gate-symbol {
          font-size: 1.8rem;
          font-weight: 700;
          color: var(--accent-color);
          margin-bottom: 0.5rem;
        }

        .gate-description {
          font-size: 1.2rem;
          color: var(--text-secondary);
        }

        .circuit-area {
          margin-bottom: 2rem;
        }

        .circuit-canvas {
          background: var(--primary-bg);
          border: 2px dashed var(--border-color);
          border-radius: var(--border-radius);
          min-height: 200px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2rem;
        }

        .empty-circuit {
          text-align: center;
          color: var(--text-muted);
        }

        .empty-circuit i {
          font-size: 3rem;
          margin-bottom: 1rem;
          opacity: 0.5;
        }

        .circuit-display {
          display: flex;
          align-items: center;
          gap: 2rem;
        }

        .gate-component {
          display: flex;
          align-items: center;
          background: var(--surface-card);
          border: 2px solid var(--accent-color);
          border-radius: var(--border-radius);
          padding: 1rem;
          box-shadow: var(--shadow-glow);
        }

        .gate-inputs, .gate-output {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .input-pin, .output-pin {
          background: var(--accent-color);
          color: white;
          padding: 0.3rem 0.6rem;
          border-radius: 4px;
          font-size: 1rem;
          font-weight: 600;
        }

        .gate-body {
          background: var(--gradient-primary);
          color: white;
          padding: 1rem 2rem;
          margin: 0 1rem;
          border-radius: var(--border-radius);
          font-weight: 700;
          font-size: 1.4rem;
        }

        .success-message {
          background: rgba(16, 185, 129, 0.1);
          border: 2px solid #10b981;
          border-radius: var(--border-radius);
          padding: 2rem;
          text-align: center;
          margin-bottom: 2rem;
          color: #10b981;
        }

        .success-message i {
          font-size: 3rem;
          margin-bottom: 1rem;
        }

        .puzzle-controls {
          display: flex;
          justify-content: center;
          gap: 1rem;
          margin-bottom: 2rem;
        }

        .control-btn {
          padding: 1rem 2rem;
          border-radius: var(--border-radius);
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 0.8rem;
          font-weight: 600;
        }

        .control-btn.reset {
          background: var(--surface-glass);
          color: var(--text-primary);
          border: 1px solid var(--border-color);
        }

        .control-btn.hint {
          background: rgba(245, 158, 11, 0.2);
          color: #f59e0b;
          border: 1px solid #f59e0b;
        }

        .control-btn.next {
          background: var(--gradient-primary);
          color: white;
        }

        .control-btn:hover:not(:disabled) {
          transform: translateY(-2px);
        }

        .control-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .solution-hint {
          background: rgba(245, 158, 11, 0.1);
          border: 1px solid #f59e0b;
          border-radius: var(--border-radius);
          padding: 2rem;
          color: #f59e0b;
        }

        @media (max-width: 768px) {
          .gate-buttons {
            flex-direction: column;
            align-items: center;
          }
          
          .gate-btn {
            width: 100%;
            max-width: 200px;
          }
          
          .puzzle-controls {
            flex-direction: column;
            align-items: center;
          }
          
          .control-btn {
            width: 100%;
            max-width: 200px;
          }
        }
      `}</style>
    </div>
  );
}

function getGateDescription(gateType: string): string {
  switch (gateType) {
    case 'AND': return 'Output TRUE when both inputs are TRUE';
    case 'OR': return 'Output TRUE when at least one input is TRUE';
    case 'NOT': return 'Output opposite of input';
    case 'XOR': return 'Output TRUE when inputs are different';
    case 'NAND': return 'Output FALSE only when both inputs are TRUE';
    case 'NOR': return 'Output TRUE only when both inputs are FALSE';
    default: return 'Logic gate';
  }
}
