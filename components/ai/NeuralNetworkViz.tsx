"use client";
import { useEffect, useRef, useState } from 'react';

interface Node {
  x: number;
  y: number;
  value: number;
  active: boolean;
}

interface Connection {
  from: Node;
  to: Node;
  weight: number;
  active: boolean;
}

export default function NeuralNetworkViz() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [nodes, setNodes] = useState<Node[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);
  const animationRef = useRef<number>();

  useEffect(() => {
    initializeNetwork();
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  const initializeNetwork = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const width = canvas.width;
    const height = canvas.height;

    // Create layers: input (4 nodes), hidden (6 nodes), output (3 nodes)
    const newNodes: Node[] = [];
    const newConnections: Connection[] = [];

    // Input layer
    for (let i = 0; i < 4; i++) {
      newNodes.push({
        x: 80,
        y: (height / 5) * (i + 1),
        value: Math.random(),
        active: false
      });
    }

    // Hidden layer
    for (let i = 0; i < 6; i++) {
      newNodes.push({
        x: width / 2,
        y: (height / 7) * (i + 1),
        value: Math.random(),
        active: false
      });
    }

    // Output layer
    for (let i = 0; i < 3; i++) {
      newNodes.push({
        x: width - 80,
        y: (height / 4) * (i + 1),
        value: Math.random(),
        active: false
      });
    }

    // Create connections
    // Input to hidden
    for (let i = 0; i < 4; i++) {
      for (let j = 4; j < 10; j++) {
        newConnections.push({
          from: newNodes[i],
          to: newNodes[j],
          weight: Math.random() * 2 - 1,
          active: false
        });
      }
    }

    // Hidden to output
    for (let i = 4; i < 10; i++) {
      for (let j = 10; j < 13; j++) {
        newConnections.push({
          from: newNodes[i],
          to: newNodes[j],
          weight: Math.random() * 2 - 1,
          active: false
        });
      }
    }

    setNodes(newNodes);
    setConnections(newConnections);
  };

  const startAnimation = () => {
    setIsRunning(true);
    animate();
  };

  const stopAnimation = () => {
    setIsRunning(false);
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
  };

  const animate = () => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw connections
    connections.forEach(conn => {
      ctx.beginPath();
      ctx.moveTo(conn.from.x, conn.from.y);
      ctx.lineTo(conn.to.x, conn.to.y);
      
      const opacity = conn.active ? 0.8 : 0.2;
      const weight = Math.abs(conn.weight);
      ctx.strokeStyle = conn.weight > 0 
        ? `rgba(0, 212, 255, ${opacity})` 
        : `rgba(239, 68, 68, ${opacity})`;
      ctx.lineWidth = weight * 3 + 1;
      ctx.stroke();
    });

    // Draw nodes
    nodes.forEach((node, index) => {
      ctx.beginPath();
      ctx.arc(node.x, node.y, 15, 0, 2 * Math.PI);
      
      if (node.active) {
        ctx.fillStyle = '#00d4ff';
        ctx.shadowColor = '#00d4ff';
        ctx.shadowBlur = 20;
      } else {
        ctx.fillStyle = '#1a2040';
        ctx.shadowBlur = 0;
      }
      
      ctx.fill();
      ctx.strokeStyle = '#00d4ff';
      ctx.lineWidth = 2;
      ctx.stroke();

      // Draw node value
      ctx.fillStyle = '#ffffff';
      ctx.font = '10px monospace';
      ctx.textAlign = 'center';
      ctx.fillText(node.value.toFixed(2), node.x, node.y + 3);
    });

    // Simulate forward propagation
    if (isRunning) {
      simulateForwardPass();
      animationRef.current = requestAnimationFrame(animate);
    }
  };

  const simulateForwardPass = () => {
    // Activate input nodes randomly
    setNodes(prevNodes => {
      const newNodes = [...prevNodes];
      
      // Reset all activations
      newNodes.forEach(node => node.active = false);
      
      // Randomly activate input nodes
      for (let i = 0; i < 4; i++) {
        if (Math.random() > 0.7) {
          newNodes[i].active = true;
          newNodes[i].value = Math.random();
        }
      }

      // Propagate to hidden layer
      setTimeout(() => {
        for (let i = 4; i < 10; i++) {
          let sum = 0;
          for (let j = 0; j < 4; j++) {
            if (newNodes[j].active) {
              sum += newNodes[j].value * Math.random();
            }
          }
          newNodes[i].active = sum > 0.5;
          newNodes[i].value = Math.tanh(sum);
        }
      }, 200);

      // Propagate to output layer
      setTimeout(() => {
        for (let i = 10; i < 13; i++) {
          let sum = 0;
          for (let j = 4; j < 10; j++) {
            if (newNodes[j].active) {
              sum += newNodes[j].value * Math.random();
            }
          }
          newNodes[i].active = sum > 0.3;
          newNodes[i].value = Math.sigmoid(sum);
        }
      }, 400);

      return newNodes;
    });

    // Activate connections based on node activity
    setConnections(prevConnections => {
      return prevConnections.map(conn => ({
        ...conn,
        active: conn.from.active && Math.random() > 0.6
      }));
    });
  };

  // Helper function
  const sigmoid = (x: number) => 1 / (1 + Math.exp(-x));

  return (
    <div className="neural-network-container">
      <div className="neural-header">
        <h3>Neural Network Visualization</h3>
        <p>Watch AI neurons process information in real-time</p>
      </div>
      
      <div className="neural-canvas-wrapper">
        <canvas
          ref={canvasRef}
          width={600}
          height={400}
          className="neural-canvas"
        />
        
        <div className="neural-legend">
          <div className="legend-item">
            <div className="legend-color positive"></div>
            <span>Positive Weight</span>
          </div>
          <div className="legend-item">
            <div className="legend-color negative"></div>
            <span>Negative Weight</span>
          </div>
          <div className="legend-item">
            <div className="legend-color active"></div>
            <span>Active Neuron</span>
          </div>
        </div>
      </div>

      <div className="neural-controls">
        <button
          onClick={isRunning ? stopAnimation : startAnimation}
          className={`neural-btn ${isRunning ? 'stop' : 'start'}`}
        >
          {isRunning ? 'Stop Processing' : 'Start AI Processing'}
        </button>
        <button onClick={initializeNetwork} className="neural-btn reset">
          Reset Network
        </button>
      </div>

      <style jsx>{`
        .neural-network-container {
          background: var(--surface-card);
          backdrop-filter: blur(20px);
          border-radius: var(--border-radius-lg);
          padding: 3rem;
          border: 1px solid var(--border-color);
          margin: 2rem 0;
        }

        .neural-header {
          text-align: center;
          margin-bottom: 2rem;
        }

        .neural-header h3 {
          font-size: 2.4rem;
          margin-bottom: 1rem;
          background: var(--gradient-primary);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .neural-canvas-wrapper {
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 2rem;
        }

        .neural-canvas {
          background: var(--primary-bg);
          border-radius: var(--border-radius);
          border: 2px solid var(--border-color);
          box-shadow: var(--shadow-md);
        }

        .neural-legend {
          display: flex;
          gap: 2rem;
          margin-top: 1rem;
          justify-content: center;
        }

        .legend-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 1.2rem;
          color: var(--text-secondary);
        }

        .legend-color {
          width: 20px;
          height: 4px;
          border-radius: 2px;
        }

        .legend-color.positive {
          background: #00d4ff;
          box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .legend-color.negative {
          background: #ef4444;
          box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
        }

        .legend-color.active {
          background: #00d4ff;
          box-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
          animation: pulse 1s ease-in-out infinite;
        }

        .neural-controls {
          display: flex;
          gap: 1.5rem;
          justify-content: center;
        }

        .neural-btn {
          padding: 1rem 2rem;
          border-radius: var(--border-radius);
          border: none;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
        }

        .neural-btn.start {
          background: var(--gradient-primary);
          color: white;
          box-shadow: var(--shadow-glow);
        }

        .neural-btn.stop {
          background: var(--gradient-neural);
          color: white;
          box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
        }

        .neural-btn.reset {
          background: var(--surface-glass);
          color: var(--text-primary);
          border: 1px solid var(--border-color);
        }

        .neural-btn:hover {
          transform: translateY(-2px) scale(1.05);
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }

        @media (max-width: 768px) {
          .neural-canvas {
            width: 100%;
            max-width: 500px;
            height: 300px;
          }
          
          .neural-controls {
            flex-direction: column;
            align-items: center;
          }
          
          .neural-btn {
            width: 100%;
            max-width: 200px;
          }
        }
      `}</style>
    </div>
  );
}
