"use client";
import { useState, useEffect, useRef } from 'react';

interface DataPoint {
  id: string;
  value: number;
  timestamp: number;
  processed: boolean;
  category: 'sensor' | 'user' | 'system' | 'ai';
}

interface ProcessingStage {
  name: string;
  description: string;
  color: string;
  icon: string;
}

const processingStages: ProcessingStage[] = [
  {
    name: 'Data Ingestion',
    description: 'Collecting raw data from multiple sources',
    color: '#00d4ff',
    icon: 'fas fa-download'
  },
  {
    name: 'AI Processing',
    description: 'Machine learning algorithms analyze patterns',
    color: '#6366f1',
    icon: 'fas fa-brain'
  },
  {
    name: 'Pattern Recognition',
    description: 'Identifying trends and anomalies',
    color: '#8b5cf6',
    icon: 'fas fa-search'
  },
  {
    name: 'Intelligent Output',
    description: 'Generating actionable insights',
    color: '#ec4899',
    icon: 'fas fa-lightbulb'
  }
];

export default function DataProcessingViz() {
  const [dataPoints, setDataPoints] = useState<DataPoint[]>([]);
  const [currentStage, setCurrentStage] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedCount, setProcessedCount] = useState(0);
  const [throughput, setThroughput] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout>();
  const stageIntervalRef = useRef<NodeJS.Timeout>();

  const generateDataPoint = (): DataPoint => {
    const categories: DataPoint['category'][] = ['sensor', 'user', 'system', 'ai'];
    return {
      id: `data-${Date.now()}-${Math.random()}`,
      value: Math.random() * 100,
      timestamp: Date.now(),
      processed: false,
      category: categories[Math.floor(Math.random() * categories.length)]
    };
  };

  const startProcessing = () => {
    setIsProcessing(true);
    setProcessedCount(0);
    
    // Generate data points
    intervalRef.current = setInterval(() => {
      setDataPoints(prev => {
        const newPoints = [...prev, generateDataPoint()];
        return newPoints.slice(-20); // Keep only last 20 points
      });
    }, 500);

    // Process data points
    const processInterval = setInterval(() => {
      setDataPoints(prev => {
        const updated = prev.map(point => {
          if (!point.processed && Math.random() > 0.3) {
            setProcessedCount(count => count + 1);
            return { ...point, processed: true };
          }
          return point;
        });
        return updated;
      });
    }, 800);

    // Cycle through processing stages
    stageIntervalRef.current = setInterval(() => {
      setCurrentStage(prev => (prev + 1) % processingStages.length);
    }, 2000);

    // Calculate throughput
    const throughputInterval = setInterval(() => {
      setThroughput(Math.floor(Math.random() * 500) + 200);
    }, 1000);

    // Cleanup function
    return () => {
      clearInterval(processInterval);
      clearInterval(throughputInterval);
    };
  };

  const stopProcessing = () => {
    setIsProcessing(false);
    if (intervalRef.current) clearInterval(intervalRef.current);
    if (stageIntervalRef.current) clearInterval(stageIntervalRef.current);
  };

  useEffect(() => {
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
      if (stageIntervalRef.current) clearInterval(stageIntervalRef.current);
    };
  }, []);

  const getCategoryColor = (category: DataPoint['category']) => {
    switch (category) {
      case 'sensor': return '#00d4ff';
      case 'user': return '#10b981';
      case 'system': return '#f59e0b';
      case 'ai': return '#8b5cf6';
      default: return '#6b7280';
    }
  };

  const getCategoryIcon = (category: DataPoint['category']) => {
    switch (category) {
      case 'sensor': return 'fas fa-thermometer-half';
      case 'user': return 'fas fa-user';
      case 'system': return 'fas fa-server';
      case 'ai': return 'fas fa-robot';
      default: return 'fas fa-circle';
    }
  };

  return (
    <div className="data-processing-viz">
      <div className="viz-header">
        <h3>Real-Time AI Data Processing</h3>
        <p>Watch how AI systems process and analyze data streams in real-time</p>
      </div>

      <div className="processing-pipeline">
        <h4>AI Processing Pipeline</h4>
        <div className="pipeline-stages">
          {processingStages.map((stage, index) => (
            <div 
              key={stage.name}
              className={`pipeline-stage ${index === currentStage ? 'active' : ''}`}
              style={{ '--stage-color': stage.color } as React.CSSProperties}
            >
              <div className="stage-icon">
                <i className={stage.icon}></i>
              </div>
              <div className="stage-content">
                <h5>{stage.name}</h5>
                <p>{stage.description}</p>
              </div>
              {index < processingStages.length - 1 && (
                <div className="stage-connector">
                  <div className="connector-line"></div>
                  <div className="connector-arrow">→</div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="data-stream">
        <h4>Live Data Stream</h4>
        <div className="stream-container">
          <div className="stream-header">
            <div className="stream-stats">
              <div className="stat">
                <span className="stat-label">Throughput:</span>
                <span className="stat-value">{throughput} ops/sec</span>
              </div>
              <div className="stat">
                <span className="stat-label">Processed:</span>
                <span className="stat-value">{processedCount}</span>
              </div>
              <div className="stat">
                <span className="stat-label">Active:</span>
                <span className="stat-value">{dataPoints.length}</span>
              </div>
            </div>
          </div>
          
          <div className="data-points">
            {dataPoints.map(point => (
              <div 
                key={point.id}
                className={`data-point ${point.processed ? 'processed' : 'pending'}`}
                style={{ '--category-color': getCategoryColor(point.category) } as React.CSSProperties}
              >
                <div className="point-icon">
                  <i className={getCategoryIcon(point.category)}></i>
                </div>
                <div className="point-info">
                  <div className="point-category">{point.category}</div>
                  <div className="point-value">{point.value.toFixed(1)}</div>
                </div>
                <div className="point-status">
                  {point.processed ? (
                    <i className="fas fa-check-circle processed-icon"></i>
                  ) : (
                    <div className="processing-spinner"></div>
                  )}
                </div>
              </div>
            ))}
            
            {dataPoints.length === 0 && (
              <div className="empty-stream">
                <i className="fas fa-stream"></i>
                <p>Start processing to see live data stream</p>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="ai-insights">
        <h4>AI-Generated Insights</h4>
        <div className="insights-grid">
          <div className="insight-card">
            <div className="insight-icon">
              <i className="fas fa-chart-line"></i>
            </div>
            <div className="insight-content">
              <h5>Trend Analysis</h5>
              <p>Data shows 23% increase in sensor activity</p>
            </div>
          </div>
          
          <div className="insight-card">
            <div className="insight-icon">
              <i className="fas fa-exclamation-triangle"></i>
            </div>
            <div className="insight-content">
              <h5>Anomaly Detection</h5>
              <p>2 unusual patterns detected in user data</p>
            </div>
          </div>
          
          <div className="insight-card">
            <div className="insight-icon">
              <i className="fas fa-bullseye"></i>
            </div>
            <div className="insight-content">
              <h5>Prediction</h5>
              <p>System load expected to peak in 15 minutes</p>
            </div>
          </div>
        </div>
      </div>

      <div className="viz-controls">
        <button
          onClick={isProcessing ? stopProcessing : startProcessing}
          className={`control-btn ${isProcessing ? 'stop' : 'start'}`}
        >
          {isProcessing ? (
            <>
              <i className="fas fa-stop"></i>
              Stop Processing
            </>
          ) : (
            <>
              <i className="fas fa-play"></i>
              Start AI Processing
            </>
          )}
        </button>
      </div>

      <style jsx>{`
        .data-processing-viz {
          background: var(--surface-card);
          backdrop-filter: blur(20px);
          border-radius: var(--border-radius-lg);
          padding: 3rem;
          border: 1px solid var(--border-color);
          margin: 2rem 0;
        }

        .viz-header {
          text-align: center;
          margin-bottom: 3rem;
        }

        .viz-header h3 {
          font-size: 2.4rem;
          margin-bottom: 1rem;
          background: var(--gradient-primary);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .processing-pipeline {
          margin-bottom: 3rem;
        }

        .processing-pipeline h4 {
          margin-bottom: 2rem;
          color: var(--text-primary);
        }

        .pipeline-stages {
          display: flex;
          align-items: center;
          gap: 1rem;
          overflow-x: auto;
          padding: 1rem 0;
        }

        .pipeline-stage {
          background: var(--tertiary-bg);
          border-radius: var(--border-radius);
          padding: 2rem;
          min-width: 250px;
          transition: all 0.4s ease;
          border: 2px solid transparent;
          position: relative;
        }

        .pipeline-stage.active {
          border-color: var(--stage-color);
          background: rgba(var(--stage-color-rgb, 0, 212, 255), 0.1);
          transform: scale(1.05);
          box-shadow: 0 0 20px rgba(var(--stage-color-rgb, 0, 212, 255), 0.3);
        }

        .stage-icon {
          width: 4rem;
          height: 4rem;
          background: var(--stage-color, var(--accent-color));
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.8rem;
          color: white;
          margin-bottom: 1rem;
        }

        .stage-content h5 {
          margin-bottom: 0.5rem;
          color: var(--text-primary);
        }

        .stage-content p {
          font-size: 1.2rem;
          color: var(--text-secondary);
          margin-bottom: 0;
        }

        .stage-connector {
          position: absolute;
          right: -2rem;
          top: 50%;
          transform: translateY(-50%);
          display: flex;
          align-items: center;
        }

        .connector-line {
          width: 2rem;
          height: 2px;
          background: var(--border-color);
        }

        .connector-arrow {
          color: var(--accent-color);
          font-size: 1.5rem;
          margin-left: 0.5rem;
        }

        .data-stream {
          margin-bottom: 3rem;
        }

        .stream-container {
          background: var(--primary-bg);
          border-radius: var(--border-radius);
          border: 1px solid var(--border-color);
          overflow: hidden;
        }

        .stream-header {
          background: var(--tertiary-bg);
          padding: 1.5rem;
          border-bottom: 1px solid var(--border-color);
        }

        .stream-stats {
          display: flex;
          gap: 3rem;
          justify-content: center;
        }

        .stat {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
        }

        .stat-label {
          font-size: 1.2rem;
          color: var(--text-secondary);
        }

        .stat-value {
          font-size: 1.8rem;
          font-weight: 700;
          color: var(--accent-color);
        }

        .data-points {
          padding: 2rem;
          max-height: 400px;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .data-point {
          display: flex;
          align-items: center;
          gap: 1.5rem;
          background: var(--surface-card);
          padding: 1.5rem;
          border-radius: var(--border-radius);
          border-left: 4px solid var(--category-color);
          transition: all 0.3s ease;
        }

        .data-point.processed {
          opacity: 0.7;
          transform: scale(0.98);
        }

        .point-icon {
          width: 4rem;
          height: 4rem;
          background: var(--category-color);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 1.6rem;
        }

        .point-info {
          flex: 1;
        }

        .point-category {
          font-size: 1.2rem;
          color: var(--text-secondary);
          text-transform: capitalize;
        }

        .point-value {
          font-size: 1.6rem;
          font-weight: 600;
          color: var(--text-primary);
        }

        .point-status {
          width: 3rem;
          height: 3rem;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .processed-icon {
          color: #10b981;
          font-size: 2rem;
        }

        .processing-spinner {
          width: 2rem;
          height: 2rem;
          border: 2px solid var(--border-color);
          border-top: 2px solid var(--accent-color);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .empty-stream {
          text-align: center;
          padding: 4rem;
          color: var(--text-muted);
        }

        .empty-stream i {
          font-size: 4rem;
          margin-bottom: 1rem;
          opacity: 0.5;
        }

        .ai-insights {
          margin-bottom: 3rem;
        }

        .insights-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 2rem;
        }

        .insight-card {
          background: var(--surface-glass);
          border-radius: var(--border-radius);
          padding: 2rem;
          border: 1px solid var(--border-color);
          display: flex;
          align-items: center;
          gap: 1.5rem;
          transition: all 0.3s ease;
        }

        .insight-card:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-md);
        }

        .insight-icon {
          width: 5rem;
          height: 5rem;
          background: var(--gradient-primary);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 2rem;
          color: white;
        }

        .insight-content h5 {
          margin-bottom: 0.5rem;
          color: var(--text-primary);
        }

        .insight-content p {
          font-size: 1.3rem;
          color: var(--text-secondary);
          margin-bottom: 0;
        }

        .viz-controls {
          text-align: center;
        }

        .control-btn {
          background: var(--gradient-primary);
          color: white;
          border: none;
          padding: 1.5rem 3rem;
          border-radius: 50px;
          font-size: 1.6rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.4s ease;
          display: inline-flex;
          align-items: center;
          gap: 1rem;
        }

        .control-btn.stop {
          background: var(--gradient-neural);
        }

        .control-btn:hover {
          transform: translateY(-3px) scale(1.05);
          box-shadow: var(--shadow-glow);
        }

        @media (max-width: 768px) {
          .pipeline-stages {
            flex-direction: column;
            align-items: stretch;
          }
          
          .pipeline-stage {
            min-width: auto;
          }
          
          .stage-connector {
            display: none;
          }
          
          .stream-stats {
            flex-direction: column;
            gap: 1rem;
          }
          
          .insights-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
}
