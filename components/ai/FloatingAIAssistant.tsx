"use client";
import { useState, useEffect } from 'react';

interface Message {
  id: string;
  text: string;
  isAI: boolean;
  timestamp: number;
}

const aiResponses = [
  "Hello! I'm your AI assistant. How can I help you explore our AI solutions?",
  "I can help you understand our neural networks, machine learning models, and AI services.",
  "Our AI systems process over 1000 predictions per second with 99% accuracy!",
  "Would you like to see a demo of our pattern recognition capabilities?",
  "I'm powered by advanced natural language processing. Ask me anything about AI!",
  "Our team specializes in custom AI solutions for businesses of all sizes.",
  "Machine learning is transforming industries. Let me show you how we can help.",
  "I can explain how our AI algorithms work in simple terms. What interests you most?"
];

export default function FloatingAIAssistant() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showPulse, setShowPulse] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    // Show initial greeting after a delay
    const timer = setTimeout(() => {
      addAIMessage("👋 Hi! I'm your AI assistant. Click to chat with me!");
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Hide pulse after user interacts
    if (isOpen) {
      setShowPulse(false);
    }
  }, [isOpen]);

  const addAIMessage = (text: string) => {
    const message: Message = {
      id: `ai-${Date.now()}`,
      text,
      isAI: true,
      timestamp: Date.now()
    };
    setMessages(prev => [...prev, message]);
  };

  const addUserMessage = (text: string) => {
    const message: Message = {
      id: `user-${Date.now()}`,
      text,
      isAI: false,
      timestamp: Date.now()
    };
    setMessages(prev => [...prev, message]);
  };

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    addUserMessage(inputValue);
    setInputValue('');
    setIsTyping(true);

    // Simulate AI thinking time
    setTimeout(() => {
      const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];
      addAIMessage(randomResponse);
      setIsTyping(false);
    }, 1000 + Math.random() * 2000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
    if (!isOpen && messages.length === 0) {
      setTimeout(() => {
        addAIMessage("Hello! I'm your AI assistant. How can I help you today?");
      }, 500);
    }
  };

  // Prevent hydration mismatch
  if (!isMounted) {
    return null;
  }

  return (
    <>
      <div className={`ai-assistant-container ${isOpen ? 'open' : ''}`}>
        {isOpen && (
          <div className="chat-window">
            <div className="chat-header">
              <div className="ai-avatar">
                <i className="fas fa-robot"></i>
              </div>
              <div className="ai-info">
                <h4>AI Assistant</h4>
                <span className="status">Online • Powered by Neural Networks</span>
              </div>
              <button onClick={toggleChat} className="close-btn">
                <i className="fas fa-times"></i>
              </button>
            </div>

            <div className="chat-messages">
              {messages.map(message => (
                <div key={message.id} className={`message ${message.isAI ? 'ai' : 'user'}`}>
                  {message.isAI && (
                    <div className="message-avatar">
                      <i className="fas fa-robot"></i>
                    </div>
                  )}
                  <div className="message-content">
                    <div className="message-bubble">
                      {message.text}
                    </div>
                    <div className="message-time">
                      {new Date(message.timestamp).toLocaleTimeString([], { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </div>
                  </div>
                </div>
              ))}
              
              {isTyping && (
                <div className="message ai">
                  <div className="message-avatar">
                    <i className="fas fa-robot"></i>
                  </div>
                  <div className="message-content">
                    <div className="message-bubble typing">
                      <div className="typing-indicator">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="chat-input">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me about AI solutions..."
                className="input-field"
              />
              <button onClick={handleSendMessage} className="send-btn">
                <i className="fas fa-paper-plane"></i>
              </button>
            </div>
          </div>
        )}

        <button 
          onClick={toggleChat} 
          className={`ai-assistant-btn ${showPulse ? 'pulse' : ''}`}
        >
          <i className="fas fa-robot"></i>
          {!isOpen && (
            <div className="notification-badge">
              <span>AI</span>
            </div>
          )}
        </button>
      </div>

      <style jsx>{`
        .ai-assistant-container {
          position: fixed;
          bottom: 2rem;
          right: 2rem;
          z-index: 1000;
          transition: all 0.3s ease;
        }

        .chat-window {
          position: absolute;
          bottom: 8rem;
          right: 0;
          width: 380px;
          height: 500px;
          background: var(--surface-card);
          backdrop-filter: blur(20px);
          border-radius: var(--border-radius-lg);
          border: 1px solid var(--border-color);
          box-shadow: var(--shadow-lg);
          display: flex;
          flex-direction: column;
          overflow: hidden;
          animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .chat-header {
          background: var(--gradient-primary);
          color: white;
          padding: 1.5rem;
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .ai-avatar {
          width: 4rem;
          height: 4rem;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.8rem;
        }

        .ai-info {
          flex: 1;
        }

        .ai-info h4 {
          margin: 0 0 0.2rem 0;
          font-size: 1.6rem;
        }

        .status {
          font-size: 1.2rem;
          opacity: 0.8;
        }

        .close-btn {
          background: none;
          border: none;
          color: white;
          font-size: 1.6rem;
          cursor: pointer;
          padding: 0.5rem;
          border-radius: 50%;
          transition: background 0.3s ease;
        }

        .close-btn:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        .chat-messages {
          flex: 1;
          padding: 1.5rem;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .message {
          display: flex;
          gap: 1rem;
          align-items: flex-start;
        }

        .message.user {
          flex-direction: row-reverse;
        }

        .message-avatar {
          width: 3rem;
          height: 3rem;
          background: var(--gradient-primary);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 1.2rem;
          flex-shrink: 0;
        }

        .message-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .message.user .message-content {
          align-items: flex-end;
        }

        .message-bubble {
          background: var(--tertiary-bg);
          padding: 1rem 1.5rem;
          border-radius: 1.5rem;
          max-width: 80%;
          word-wrap: break-word;
          line-height: 1.4;
        }

        .message.user .message-bubble {
          background: var(--gradient-primary);
          color: white;
        }

        .message-bubble.typing {
          background: var(--tertiary-bg);
          padding: 1rem;
        }

        .typing-indicator {
          display: flex;
          gap: 0.3rem;
          align-items: center;
        }

        .typing-indicator span {
          width: 0.6rem;
          height: 0.6rem;
          background: var(--accent-color);
          border-radius: 50%;
          animation: typing 1.4s infinite;
        }

        .typing-indicator span:nth-child(2) {
          animation-delay: 0.2s;
        }

        .typing-indicator span:nth-child(3) {
          animation-delay: 0.4s;
        }

        @keyframes typing {
          0%, 60%, 100% {
            transform: translateY(0);
            opacity: 0.4;
          }
          30% {
            transform: translateY(-10px);
            opacity: 1;
          }
        }

        .message-time {
          font-size: 1rem;
          color: var(--text-muted);
        }

        .chat-input {
          padding: 1.5rem;
          border-top: 1px solid var(--border-color);
          display: flex;
          gap: 1rem;
          align-items: center;
        }

        .input-field {
          flex: 1;
          padding: 1rem 1.5rem;
          border: 1px solid var(--border-color);
          border-radius: 2rem;
          background: var(--tertiary-bg);
          color: var(--text-primary);
          font-size: 1.4rem;
          outline: none;
          transition: border-color 0.3s ease;
        }

        .input-field:focus {
          border-color: var(--accent-color);
        }

        .send-btn {
          width: 4rem;
          height: 4rem;
          background: var(--gradient-primary);
          border: none;
          border-radius: 50%;
          color: white;
          font-size: 1.4rem;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .send-btn:hover {
          transform: scale(1.1);
          box-shadow: var(--shadow-glow);
        }

        .ai-assistant-btn {
          width: 6rem;
          height: 6rem;
          background: var(--gradient-primary);
          border: none;
          border-radius: 50%;
          color: white;
          font-size: 2.5rem;
          cursor: pointer;
          box-shadow: var(--shadow-lg);
          transition: all 0.3s ease;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .ai-assistant-btn:hover {
          transform: scale(1.1);
          box-shadow: var(--shadow-glow);
        }

        .ai-assistant-btn.pulse {
          animation: pulse-btn 2s infinite;
        }

        @keyframes pulse-btn {
          0% {
            box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7);
          }
          70% {
            box-shadow: 0 0 0 20px rgba(0, 212, 255, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(0, 212, 255, 0);
          }
        }

        .notification-badge {
          position: absolute;
          top: -0.5rem;
          right: -0.5rem;
          background: #ef4444;
          color: white;
          border-radius: 50%;
          width: 2.5rem;
          height: 2.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1rem;
          font-weight: 600;
          animation: bounce-badge 2s infinite;
        }

        @keyframes bounce-badge {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
          }
          40% {
            transform: translateY(-5px);
          }
          60% {
            transform: translateY(-3px);
          }
        }

        @media (max-width: 768px) {
          .ai-assistant-container {
            bottom: 1rem;
            right: 1rem;
          }
          
          .chat-window {
            width: 320px;
            height: 450px;
            bottom: 7rem;
          }
          
          .ai-assistant-btn {
            width: 5rem;
            height: 5rem;
            font-size: 2rem;
          }
        }

        @media (max-width: 480px) {
          .chat-window {
            width: calc(100vw - 2rem);
            right: -1rem;
          }
        }
      `}</style>
    </>
  );
}
