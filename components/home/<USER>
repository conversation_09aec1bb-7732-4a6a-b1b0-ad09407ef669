export default function Process() {
  return (
    <section id="process" className="section">
      <div className="container">
        <div className="section-header">
          <span className="section-subtitle">How We Work</span>
          <h2 className="section-title">Our Process</h2>
          <p className="section-description">
            We follow a structured approach to deliver exceptional solutions tailored to your specific needs.
          </p>
        </div>
        <div className="process-timeline">
          <div className="timeline-node">
            <div className="node-number">01</div>
            <div className="node-content">
              <h3>Discovery</h3>
              <p>We begin with a thorough understanding of your business, goals, and challenges.</p>
            </div>
          </div>
          <div className="timeline-node">
            <div className="node-number">02</div>
            <div className="node-content">
              <h3>Strategy</h3>
              <p>Developing a comprehensive roadmap to achieve your objectives effectively.</p>
            </div>
          </div>
          <div className="timeline-node">
            <div className="node-number">03</div>
            <div className="node-content">
              <h3>Design</h3>
              <p>Creating intuitive, visually appealing interfaces that enhance user experience.</p>
            </div>
          </div>
          <div className="timeline-node">
            <div className="node-number">04</div>
            <div className="node-content">
              <h3>Development</h3>
              <p>Building robust, scalable solutions using cutting-edge technologies.</p>
            </div>
          </div>
          <div className="timeline-node">
            <div className="node-number">05</div>
            <div className="node-content">
              <h3>Testing</h3>
              <p>Rigorous quality assurance to ensure flawless performance.</p>
            </div>
          </div>
          <div className="timeline-node">
            <div className="node-number">06</div>
            <div className="node-content">
              <h3>Deployment</h3>
              <p>Smooth implementation and integration with your existing systems.</p>
            </div>
          </div>
          <div className="timeline-node">
            <div className="node-number">07</div>
            <div className="node-content">
              <h3>Support</h3>
              <p>Ongoing maintenance and assistance to ensure continued success.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}