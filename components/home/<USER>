// import Link from "next/link"

// export default function ServicesPreview() {
//   return (
//     <section id="services-preview" className="section">
//       <div className="section-pattern"></div>
//       <div className="container">
//         <div className="section-header">
//           <span className="section-subtitle">What We Offer</span>
//           <h2 className="section-title">Our Services</h2>
//           <p className="section-description">At Qoverse, we deliver cutting-edge technology solutions tailored to empower your business success.</p>
//         </div>
//         <div className="services-showcase">
//           <div className="service-card" data-service="ai">
//             <div className="service-icon">
//               <i className="fas fa-brain"></i>
//             </div>
//             <h3>Generative AI Solutions</h3>
//             <p>Custom AI models for content creation, automation, and innovative applications.</p>
//             <ul className="service-features">
//               <li>Custom LLM Development</li>
//               <li>AI Integration Services</li>
//               <li>Generative Image Solutions</li>
//             </ul>
//             <a href="#generative-ai" className="service-link">Learn More</a>
//             <div className="service-background"></div>
//           </div>
//           <div className="service-card" data-service="web">
//             <div className="service-icon">
//               <i className="fas fa-laptop-code"></i>
//             </div>
//             <h3>Web & Mobile App Development</h3>
//             <p>Scalable, intuitive platforms designed to meet your digital needs.</p>
//             <ul className="service-features">
//               <li>Progressive Web Apps</li>
//               <li>Native Mobile Development</li>
//               <li>Cross-platform Solutions</li>
//             </ul>
//             <a href="#web-mobile" className="service-link">Learn More</a>
//             <div className="service-background"></div>
//           </div>
//           <div className="service-card" data-service="data">
//             <div className="service-icon">
//               <i className="fas fa-chart-bar"></i>
//             </div>
//             <h3>Data Management & Analytics</h3>
//             <p>Advanced analytics delivering actionable insights for your business.</p>
//             <ul className="service-features">
//               <li>Big Data Processing</li>
//               <li>Predictive Analytics</li>
//               <li>Data Visualization</li>
//             </ul>
//             <a href="#data-management" className="service-link">Learn More</a>
//             <div className="service-background"></div>
//           </div>
//         </div>
//         <div className="section-cta">
//           <Link href="/services" className="cta-button tertiary">
//             <span className="button-text">View All Services</span>
//             <span className="button-icon">→</span>
//           </Link>
//         </div>
//       </div>
//     </section>
//   )
// }

import Link from "next/link"

export default function ServicesPreview() {
  return (
    <section id="services-preview" className="section">
      <div className="section-pattern"></div>
      <div className="container">
        <div className="section-header">
          <span className="section-subtitle">What We Offer</span>
          <h2 className="section-title">Our Services</h2>
          <p className="section-description">At Qoverse, we deliver cutting-edge technology solutions tailored to empower your business success.</p>
        </div>
        <div className="services-showcase">
          <div className="service-card" data-service="ai">
            <div className="service-icon">
              <i className="fas fa-brain"></i>
            </div>
            <h3>Generative AI Solutions</h3>
            <p>Custom AI models for content creation, automation, and innovative applications.</p>
            <ul className="service-features">
              <li>Custom LLM Development</li>
              <li>AI Integration Services</li>
              <li>Generative Image Solutions</li>
            </ul>
            <Link href="/services#generative-ai" className="service-link">Learn More</Link>
            <div className="service-background"></div>
          </div>
          <div className="service-card" data-service="web">
            <div className="service-icon">
              <i className="fas fa-laptop-code"></i>
            </div>
            <h3>Web & Mobile App Development</h3>
            <p>Scalable, intuitive platforms designed to meet your digital needs.</p>
            <ul className="service-features">
              <li>Progressive Web Apps</li>
              <li>Native Mobile Development</li>
              <li>Cross-platform Solutions</li>
            </ul>
            <Link href="/services#web-mobile" className="service-link">Learn More</Link>
            <div className="service-background"></div>
          </div>
          <div className="service-card" data-service="data">
            <div className="service-icon">
              <i className="fas fa-chart-bar"></i>
            </div>
            <h3>Data Management & Analytics</h3>
            <p>Advanced analytics delivering actionable insights for your business.</p>
            <ul className="service-features">
              <li>Big Data Processing</li>
              <li>Predictive Analytics</li>
              <li>Data Visualization</li>
            </ul>
            <Link href="/services#data-management" className="service-link">Learn More</Link>
            <div className="service-background"></div>
          </div>
        </div>
        <div className="section-cta">
          <Link href="/services" className="cta-button tertiary">
            <span className="button-text">View All Services</span>
            <span className="button-icon">→</span>
          </Link>
        </div>
      </div>
    </section>
  )
}