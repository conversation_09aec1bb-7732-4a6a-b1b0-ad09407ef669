"use client";
import { useState } from 'react';
import NeuralNetworkViz from '@/components/ai/NeuralNetworkViz';
import AICodeGenerator from '@/components/ai/AICodeGenerator';
import PatternRecognition from '@/components/ai/PatternRecognition';
import LogicGatePuzzle from '@/components/ai/LogicGatePuzzle';
import DataProcessingViz from '@/components/ai/DataProcessingViz';

interface ShowcaseTab {
  id: string;
  title: string;
  description: string;
  icon: string;
  component: React.ComponentType;
}

const showcaseTabs: ShowcaseTab[] = [
  {
    id: 'neural',
    title: 'Neural Networks',
    description: 'Watch AI neurons process information in real-time',
    icon: 'fas fa-brain',
    component: NeuralNetworkViz
  },
  {
    id: 'codegen',
    title: 'AI Code Generation',
    description: 'See AI generate intelligent code solutions',
    icon: 'fas fa-code',
    component: AICodeGenerator
  },
  {
    id: 'patterns',
    title: 'Pattern Recognition',
    description: 'Test your intelligence against AI pattern analysis',
    icon: 'fas fa-search',
    component: PatternRecognition
  },
  {
    id: 'logic',
    title: 'Logic Puzzles',
    description: 'Master digital logic - the foundation of AI',
    icon: 'fas fa-microchip',
    component: LogicGatePuzzle
  },
  {
    id: 'dataprocessing',
    title: 'Data Processing',
    description: 'Real-time AI data analysis and insights',
    icon: 'fas fa-stream',
    component: DataProcessingViz
  }
];

export default function AIShowcase() {
  const [activeTab, setActiveTab] = useState('neural');

  const ActiveComponent = showcaseTabs.find(tab => tab.id === activeTab)?.component || NeuralNetworkViz;

  return (
    <section id="ai-showcase" className="section">
      <div className="neural-pattern"></div>
      <div className="container">
        <div className="section-header">
          <span className="section-subtitle">Interactive AI Demos</span>
          <h2 className="section-title">Experience AI Intelligence</h2>
          <p className="section-description">
            Explore cutting-edge AI technologies through interactive demonstrations. 
            See how artificial intelligence processes information, recognizes patterns, and solves complex problems.
          </p>
        </div>

        <div className="showcase-container">
          <div className="showcase-tabs">
            {showcaseTabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`showcase-tab ${activeTab === tab.id ? 'active' : ''}`}
              >
                <div className="tab-icon">
                  <i className={tab.icon}></i>
                </div>
                <div className="tab-content">
                  <h4>{tab.title}</h4>
                  <p>{tab.description}</p>
                </div>
              </button>
            ))}
          </div>

          <div className="showcase-content">
            <div className="content-wrapper">
              <ActiveComponent />
            </div>
          </div>
        </div>

        <div className="ai-stats">
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-robot"></i>
            </div>
            <div className="stat-content">
              <div className="stat-number" data-count="99">0</div>
              <div className="stat-label">AI Accuracy</div>
              <div className="stat-unit">%</div>
            </div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-lightning-bolt"></i>
            </div>
            <div className="stat-content">
              <div className="stat-number" data-count="1000">0</div>
              <div className="stat-label">Predictions/Sec</div>
              <div className="stat-unit">+</div>
            </div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-network-wired"></i>
            </div>
            <div className="stat-content">
              <div className="stat-number" data-count="50">0</div>
              <div className="stat-label">Neural Layers</div>
              <div className="stat-unit">M</div>
            </div>
          </div>
          
          <div className="stat-item">
            <div className="stat-icon">
              <i className="fas fa-database"></i>
            </div>
            <div className="stat-content">
              <div className="stat-number" data-count="10">0</div>
              <div className="stat-label">Data Processed</div>
              <div className="stat-unit">TB</div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        #ai-showcase {
          background: var(--gradient-dark);
          position: relative;
          overflow: hidden;
        }

        .showcase-container {
          max-width: 1400px;
          margin: 0 auto;
        }

        .showcase-tabs {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 2rem;
          margin-bottom: 4rem;
        }

        .showcase-tab {
          background: var(--surface-card);
          backdrop-filter: blur(20px);
          border: 1px solid var(--border-color);
          border-radius: var(--border-radius-lg);
          padding: 2.5rem;
          cursor: pointer;
          transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
          text-align: left;
          position: relative;
          overflow: hidden;
        }

        .showcase-tab::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: var(--gradient-glass);
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: -1;
        }

        .showcase-tab:hover, .showcase-tab.active {
          transform: translateY(-8px) scale(1.02);
          border-color: var(--accent-color);
          box-shadow: var(--shadow-glow);
        }

        .showcase-tab:hover::before, .showcase-tab.active::before {
          opacity: 1;
        }

        .showcase-tab.active {
          border-color: var(--accent-color);
          box-shadow: 0 0 30px rgba(0, 212, 255, 0.4);
        }

        .tab-icon {
          width: 6rem;
          height: 6rem;
          background: var(--gradient-primary);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 2.5rem;
          color: white;
          margin-bottom: 2rem;
          transition: all 0.4s ease;
        }

        .showcase-tab:hover .tab-icon, .showcase-tab.active .tab-icon {
          transform: scale(1.1) rotate(10deg);
          box-shadow: var(--shadow-glow);
        }

        .tab-content h4 {
          font-size: 2rem;
          margin-bottom: 1rem;
          color: var(--text-primary);
        }

        .tab-content p {
          color: var(--text-secondary);
          line-height: 1.6;
          margin-bottom: 0;
        }

        .showcase-content {
          background: var(--surface-card);
          backdrop-filter: blur(20px);
          border-radius: var(--border-radius-lg);
          border: 1px solid var(--border-color);
          overflow: hidden;
          margin-bottom: 6rem;
          box-shadow: var(--shadow-lg);
        }

        .content-wrapper {
          padding: 0;
        }

        .ai-stats {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 3rem;
          margin-top: 6rem;
        }

        .stat-item {
          background: var(--surface-card);
          backdrop-filter: blur(20px);
          border-radius: var(--border-radius-lg);
          padding: 3rem;
          text-align: center;
          border: 1px solid var(--border-color);
          transition: all 0.4s ease;
          position: relative;
          overflow: hidden;
        }

        .stat-item::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: var(--gradient-neural);
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: -1;
        }

        .stat-item:hover {
          transform: translateY(-8px);
          border-color: var(--accent-color);
          box-shadow: var(--shadow-glow);
        }

        .stat-item:hover::before {
          opacity: 0.1;
        }

        .stat-icon {
          width: 7rem;
          height: 7rem;
          background: var(--gradient-primary);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 3rem;
          color: white;
          margin: 0 auto 2rem;
          transition: all 0.4s ease;
        }

        .stat-item:hover .stat-icon {
          transform: scale(1.1);
          box-shadow: var(--shadow-glow);
        }

        .stat-content {
          display: flex;
          align-items: baseline;
          justify-content: center;
          gap: 0.5rem;
          margin-bottom: 1rem;
        }

        .stat-number {
          font-family: var(--font-heading);
          font-size: 4rem;
          font-weight: 800;
          color: var(--accent-color);
          line-height: 1;
        }

        .stat-unit {
          font-size: 2rem;
          font-weight: 600;
          color: var(--accent-color);
        }

        .stat-label {
          font-size: 1.4rem;
          color: var(--text-secondary);
          font-weight: 500;
          text-align: center;
        }

        @media (max-width: 1200px) {
          .showcase-tabs {
            grid-template-columns: repeat(2, 1fr);
          }
        }

        @media (max-width: 768px) {
          .showcase-tabs {
            grid-template-columns: 1fr;
          }
          
          .showcase-tab {
            padding: 2rem;
          }
          
          .tab-icon {
            width: 5rem;
            height: 5rem;
            font-size: 2rem;
          }
          
          .ai-stats {
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
          }
          
          .stat-item {
            padding: 2rem;
          }
          
          .stat-icon {
            width: 5rem;
            height: 5rem;
            font-size: 2rem;
          }
          
          .stat-number {
            font-size: 3rem;
          }
        }

        @media (max-width: 480px) {
          .ai-stats {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </section>
  );
}
