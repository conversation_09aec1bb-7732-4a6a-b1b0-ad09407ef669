

import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function Hero() {
  const [isMobile, setIsMobile] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    window.addEventListener('mousemove', handleMouseMove);

    const initThreeBackground = async () => {
      try {
        console.log('THREE.js background initialized');
        if (isMobile) {
          console.log('Applying mobile optimizations for THREE.js');
        }
      } catch (error) {
        console.error('Failed to initialize THREE.js background:', error);
      }
    };

    initThreeBackground();
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [isMobile]);

  return (
    <>
      <section id="hero">
        {/* Enhanced Background elements */}
        <div className="neural-pattern"></div>
        <div className="ai-grid"></div>
        <div
          className="parallax-gradient"
          style={{
            background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(0, 212, 255, 0.15) 0%, rgba(99, 102, 241, 0.1) 50%, transparent 100%)`
          }}
        ></div>
        <div className="floating-particles"></div>
        <div id="three-canvas"></div>

        {/* Content container */}
        <div className="container">
          <div className="hero-content">
            {/* Hero text */}
            <div className="hero-text">
              <div className="hero-badge">
                <span className="badge-icon">🚀</span>
                <span>Next-Gen AI Solutions</span>
              </div>
              <h1 className="hero-title" data-text="Welcome to Qoverse">
                <span className="title-line">Welcome to</span>
                <span className="title-highlight">Qoverse</span>
              </h1>
              <p className="hero-subtitle">
                Where cutting-edge AI meets innovative software development.
                Transform your business with our advanced machine learning solutions and intelligent automation.
              </p>

              {/* Call to action */}
              <div className="hero-cta">
                <Link href="/services" className="cta-button primary">
                  <span className="button-text">Explore AI Solutions</span>
                  <span className="button-icon">→</span>
                </Link>
                <Link href="/contact" className="cta-button secondary">
                  <span className="button-text">Get Started</span>
                  <span className="button-icon">✨</span>
                </Link>
              </div>
            </div>
            
            {/* Enhanced Highlight cards */}
            <div className="hero-highlights">
              {/* AI-Powered card */}
              <div className="highlight-card ai-card">
                <div className="highlight-icon">
                  <i className="fas fa-brain"></i>
                </div>
                <div className="highlight-text">
                  <h3>AI-Powered</h3>
                  <p>Advanced machine learning and neural networks</p>
                </div>
                <div className="card-glow"></div>
              </div>

              {/* Machine Learning card */}
              <div className="highlight-card ml-card">
                <div className="highlight-icon">
                  <i className="fas fa-robot"></i>
                </div>
                <div className="highlight-text">
                  <h3>Intelligent</h3>
                  <p>Smart automation and predictive analytics</p>
                </div>
                <div className="card-glow"></div>
              </div>

              {/* Innovation card */}
              <div className="highlight-card innovation-card">
                <div className="highlight-icon">
                  <i className="fas fa-lightbulb"></i>
                </div>
                <div className="highlight-text">
                  <h3>Innovative</h3>
                  <p>Cutting-edge solutions for tomorrow's challenges</p>
                </div>
                <div className="card-glow"></div>
              </div>
            </div>
          </div>
        </div>
        
        {isMobile && (
          <div className="scroll-indicator">
            <i className="fas fa-chevron-down"></i>
          </div>
        )}
      </section>

      <style jsx>{`
        #hero {
          position: relative;
          min-height: 100vh;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          padding: 4rem 0;
          background: linear-gradient(135deg, #0a0b1e 0%, #151a35 50%, #1a2040 100%);
        }

        .neural-pattern, .ai-grid, .parallax-gradient, .floating-particles, #three-canvas {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 0;
        }

        .parallax-gradient {
          transition: background 0.3s ease;
          z-index: 1;
        }

        .floating-particles {
          background-image:
            radial-gradient(2px 2px at 20px 30px, rgba(0, 212, 255, 0.3), transparent),
            radial-gradient(2px 2px at 40px 70px, rgba(99, 102, 241, 0.3), transparent),
            radial-gradient(1px 1px at 90px 40px, rgba(168, 85, 247, 0.3), transparent),
            radial-gradient(1px 1px at 130px 80px, rgba(0, 212, 255, 0.3), transparent);
          background-repeat: repeat;
          background-size: 200px 100px;
          animation: float-particles 20s linear infinite;
          z-index: 1;
        }

        @keyframes float-particles {
          0% { transform: translateY(0px) translateX(0px); }
          33% { transform: translateY(-20px) translateX(10px); }
          66% { transform: translateY(-10px) translateX(-10px); }
          100% { transform: translateY(0px) translateX(0px); }
        }

        .container {
          width: 100%;
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 1.5rem;
          position: relative;
          z-index: 10;
        }

        .hero-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          width: 100%;
        }

        .hero-text {
          max-width: 100%;
          width: 100%;
          margin: 0 auto 3rem;
          position: relative;
          text-align: center;
        }

        .hero-badge {
          display: inline-flex;
          align-items: center;
          gap: 0.8rem;
          background: rgba(0, 212, 255, 0.1);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(0, 212, 255, 0.3);
          border-radius: 50px;
          padding: 0.8rem 1.5rem;
          margin-bottom: 2rem;
          font-size: 1.4rem;
          font-weight: 600;
          color: #00d4ff;
          animation: badge-glow 3s ease-in-out infinite;
        }

        .badge-icon {
          font-size: 1.6rem;
          animation: bounce-icon 2s ease-in-out infinite;
        }

        @keyframes badge-glow {
          0%, 100% { box-shadow: 0 0 20px rgba(0, 212, 255, 0.3); }
          50% { box-shadow: 0 0 30px rgba(0, 212, 255, 0.5); }
        }

        @keyframes bounce-icon {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-3px); }
        }

        .hero-title {
          font-size: 5.5rem;
          font-weight: 800;
          margin-bottom: 2rem;
          line-height: 1.1;
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .title-line {
          color: #ffffff;
          margin-bottom: 0.5rem;
        }

        .title-highlight {
          background: linear-gradient(135deg, #00d4ff 0%, #6366f1 50%, #8b5cf6 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-shadow: 0 0 40px rgba(0, 212, 255, 0.5);
          animation: title-glow 4s ease-in-out infinite;
        }

        @keyframes title-glow {
          0%, 100% { filter: brightness(1); }
          50% { filter: brightness(1.2); }
        }

        .hero-subtitle {
          font-size: 2rem;
          margin-bottom: 3rem;
          color: #cbd5e1;
          line-height: 1.6;
          max-width: 70rem;
          margin-left: auto;
          margin-right: auto;
        }

        .highlight-card {
          position: relative;
          background: rgba(21, 26, 53, 0.8);
          backdrop-filter: blur(20px);
          border-radius: 16px;
          padding: 2.5rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
          border: 1px solid rgba(0, 212, 255, 0.2);
          overflow: hidden;
        }

        .highlight-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: -1;
        }

        .highlight-card:hover {
          transform: translateY(-10px) scale(1.05);
          border-color: rgba(0, 212, 255, 0.5);
          box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
        }

        .highlight-card:hover::before {
          opacity: 1;
        }

        .card-glow {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: radial-gradient(circle at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: -1;
        }

        .highlight-card:hover .card-glow {
          opacity: 1;
        }

        .ai-card:hover {
          border-color: rgba(0, 212, 255, 0.6);
          box-shadow: 0 20px 40px rgba(0, 212, 255, 0.3);
        }

        .ml-card:hover {
          border-color: rgba(99, 102, 241, 0.6);
          box-shadow: 0 20px 40px rgba(99, 102, 241, 0.3);
        }

        .innovation-card:hover {
          border-color: rgba(168, 85, 247, 0.6);
          box-shadow: 0 20px 40px rgba(168, 85, 247, 0.3);
        }

        .highlight-text h3 {
          font-size: 1.8rem;
          font-weight: 600;
          margin-bottom: 0.75rem;
          color: #1a1a2a;
        }

        .highlight-text p {
          font-size: 1.1rem;
          color: #4a4a57;
          line-height: 1.4;
        }

        /* Force light text in cards for dark mode */
        :global(body.dark) .highlight-text h3, 
        :global(html.dark) .highlight-text h3 {
          color: #FFFFFF !important;
        }

        :global(body.dark) .highlight-text p, 
        :global(html.dark) .highlight-text p {
          color: rgba(255, 255, 255, 0.7) !important;
        }

        .hero-cta {
          display: flex;
          gap: 2rem;
          margin: 3rem 0;
          justify-content: center;
          flex-wrap: wrap;
        }

        .cta-button {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
          padding: 1.4rem 3rem;
          border-radius: 50px;
          font-size: 1.6rem;
          font-weight: 600;
          text-decoration: none;
          transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
          position: relative;
          overflow: hidden;
          backdrop-filter: blur(10px);
        }

        .cta-button::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.6s ease;
        }

        .cta-button:hover::before {
          left: 100%;
        }

        .cta-button.primary {
          background: linear-gradient(135deg, #00d4ff 0%, #6366f1 50%, #8b5cf6 100%);
          color: white;
          border: 1px solid rgba(0, 212, 255, 0.3);
          box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
        }

        .cta-button.primary:hover {
          transform: translateY(-3px) scale(1.05);
          box-shadow: 0 15px 35px rgba(0, 212, 255, 0.5);
        }

        .cta-button.secondary {
          background: rgba(21, 26, 53, 0.8);
          color: #00d4ff;
          border: 2px solid rgba(0, 212, 255, 0.5);
        }

        .cta-button.secondary:hover {
          background: rgba(0, 212, 255, 0.1);
          transform: translateY(-3px) scale(1.05);
          box-shadow: 0 15px 35px rgba(0, 212, 255, 0.3);
        }

        .hero-highlights {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 2rem;
          width: 100%;
          max-width: 1200px;
          margin: 0 auto;
        }

        .highlight-icon {
          width: 6rem;
          height: 6rem;
          border-radius: 50%;
          background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(99, 102, 241, 0.2) 100%);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(0, 212, 255, 0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 2.4rem;
          color: #00d4ff;
          margin-bottom: 2rem;
          transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
          position: relative;
          overflow: hidden;
        }

        .highlight-icon::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, #00d4ff 0%, #6366f1 100%);
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: -1;
        }

        .highlight-card:hover .highlight-icon {
          transform: scale(1.2) rotate(10deg);
          color: white;
          box-shadow: 0 10px 25px rgba(0, 212, 255, 0.4);
        }

        .highlight-card:hover .highlight-icon::before {
          opacity: 1;
        }

        .scroll-indicator {
          position: absolute;
          bottom: 2rem;
          left: 50%;
          transform: translateX(-50%);
          animation: bounce 2s infinite;
          opacity: 0.7;
          font-size: 1.5rem;
          color: #FFFFFF;
        }

        /* Override light mode styles when not in dark mode */
        @media (prefers-color-scheme: light) {
          :global(body:not(.dark)) .glitch-effect, 
          :global(html:not(.dark)) .glitch-effect {
            color: #1a1a2a !important;
            text-shadow: none;
          }
          
          :global(body:not(.dark)) .typewriter, 
          :global(html:not(.dark)) .typewriter {
            color: #333 !important;
            text-shadow: none;
          }
          
          :global(body:not(.dark)) .hero-text:before, 
          :global(html:not(.dark)) .hero-text:before {
            background: transparent;
          }
        }

        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0) translateX(-50%);
          }
          40% {
            transform: translateY(-20px) translateX(-50%);
          }
          60% {
            transform: translateY(-10px) translateX(-50%);
          }
        }

        /* Mobile styles - these will override desktop styles */
        @media (max-width: 768px) {
          #hero {
            min-height: auto;
            height: auto;
            padding: 5rem 0 2rem;
            width: 100vw;
            max-width: 100%;
            overflow-x: hidden;
          }
          
          .container {
            padding: 0 1rem;
          }
          
          .hero-text {
            max-width: 100%;
            padding: 0 1rem;
          }
          
          .glitch-effect {
            font-size: 2.8rem;
          }
          
          .typewriter {
            font-size: 1.4rem;
            white-space: normal;
            border-right: none;
            width: 100%;
            display: block;
          }
          
          .hero-highlights {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            width: 100%;
            max-width: 90%;
            margin: 1rem auto 0;
          }
          
          .highlight-card {
            width: 100%;
            margin-bottom: 1rem;
            padding: 1.2rem;
            flex-direction: row;
            align-items: center;
            gap: 1rem;
            text-align: left;
          }
          
          .highlight-icon {
            width: 3.5rem;
            height: 3.5rem;
            font-size: 1.5rem;
            margin-bottom: 0;
            min-width: 3rem;
            min-height: 3rem;
          }
          
          .highlight-text h3 {
            font-size: 1.4rem;
            margin-bottom: 0.3rem;
          }
          
          .highlight-text p {
            font-size: 0.9rem;
            line-height: 1.3;
          }
          
          .hero-cta {
            width: 100%;
            justify-content: center;
            margin: 1.5rem 0 2rem;
          }
          
          .cta-button {
            width: 100%;
            max-width: 280px;
            font-size: 1.1rem;
            padding: 0.9rem 1.8rem;
          }

          #hero::after {
            content: '';
            display: block;
            height: 2rem;
            width: 100%;
            clear: both;
          }
        }
        
        /* Very small devices */
        @media (max-width: 400px) {
          .glitch-effect {
            font-size: 2.4rem;
          }
          
          .highlight-card {
            padding: 1rem 1.2rem;
          }
          
          .highlight-icon {
            min-width: 2.8rem;
            min-height: 2.8rem;
            font-size: 1.2rem;
          }
          
          .highlight-text h3 {
            font-size: 1.3rem;
          }
          
          .cta-button {
            max-width: 90%;
          }
        }
      `}</style>
    </>
  );
}