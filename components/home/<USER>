'use client'

import { useState, useEffect } from 'react'

interface Testimonial {
  quote: string
  author: string
  position: string
}

export default function Testimonials() {
  const [activeSlide, setActiveSlide] = useState(0)
  const testimonials: Testimonial[] = [
    {
      quote:
        "We engaged Qoverse for fraud detection using <PERSON><PERSON>'s Law and Monte Carlo simulations, and they delivered precise, reliable results. Later, we relied on their expertise in applying LLMs for financial data analysis across German, UK, Polish, and Austrian markets—outstanding service from a highly capable company!",
      author: "Dr. <PERSON>",
      position: "Benford Law Analyst",
    },
    // {
    //   quote:
    //     "We turned to Qoverse for BESS optimization in electric vehicles and electrical bus route planning. Their innovative, efficient solutions exceeded expectations, proving them to be a leader in sustainable transportation services!",
    //   author: "<PERSON><PERSON>",
    //   position: "Co-Founder, MET3R",
    // },
    {
      quote:
        "Qoverse's data analytics platform provided us with insights that have significantly improved our decision-making process and operational efficiency.",
      author: "<PERSON>",
      position: "Co-Founder, <PERSON> Wolf",
    },
  ]

  const nextSlide = () => {
    setActiveSlide((prev) => (prev + 1) % testimonials.length)
  }

  const prevSlide = () => {
    setActiveSlide((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  const goToSlide = (index: number) => {
    setActiveSlide(index)
  }

  // Auto-advance slides
  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide()
    }, 6000)
    return () => clearInterval(interval)
  }, [])

  return (
    <section id="testimonials" className="section">
      <div className="container">
        <div className="section-header">
          <span className="section-subtitle">What Clients Say</span>
          <h2 className="section-title">Testimonials</h2>
          <p className="section-description">Hear from our clients about their experience working with Qoverse.</p>
        </div>
        <div className="testimonials-slider">
          {testimonials.map((testimonial, index) => (
            <div key={index} className={`testimonial-slide ${index === activeSlide ? "active" : ""}`}>
              <div className="testimonial-card">
                <div className="testimonial-quote">{testimonial.quote}</div>
                <div className="testimonial-author">
                  <div className="author-info">
                    <h4>{testimonial.author}</h4>
                    <p>{testimonial.position}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
          <div className="testimonial-controls">
            <button className="control-prev" onClick={prevSlide}>
              ←
            </button>
            <div className="control-indicators">
              {testimonials.map((_, index) => (
                <span
                  key={index}
                  className={`indicator ${index === activeSlide ? "active" : ""}`}
                  onClick={() => goToSlide(index)}
                ></span>
              ))}
            </div>
            <button className="control-next" onClick={nextSlide}>
              →
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}