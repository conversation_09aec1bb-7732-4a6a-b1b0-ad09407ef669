

import Link from 'next/link';
import { useEffect, useState } from 'react';

export default function Hero() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    
    const initThreeBackground = async () => {
      try {
        console.log('THREE.js background initialized');
        if (isMobile) {
          console.log('Applying mobile optimizations for THREE.js');
        }
      } catch (error) {
        console.error('Failed to initialize THREE.js background:', error);
      }
    };
    
    initThreeBackground();
    return () => window.removeEventListener('resize', handleResize);
  }, [isMobile]);

  return (
    <>
      <section id="hero">
        {/* Background elements */}
        <div className="parallax"></div>
        <div id="three-canvas"></div>
        
        {/* Content container */}
        <div className="container">
          <div className="hero-content">
            {/* Hero text */}
            <div className="hero-text">
              <h1 className="glitch-effect" data-text="Welcome to Qoverse">
                Welcome to Qoverse
              </h1>
              <p className="typewriter">
                Where expertise and innovation converge for transformative solutions.
              </p>
              
              {/* Call to action */}
              <div className="hero-cta">
                <Link href="/services" className="cta-button primary">
                  <span className="button-text">Discover Our Services</span>
                  <span className="button-icon">→</span>
                </Link>
              </div>
            </div>
            
            {/* Highlight cards */}
            <div className="hero-highlights">
              {/* AI-Powered card */}
              <div className="highlight-card">
                <div className="highlight-icon">
                  <i className="fas fa-brain"></i>
                </div>
                <div className="highlight-text">
                  <h3>AI-Powered</h3>
                  <p>Advanced solutions leveraging latest AI technologies</p>
                </div>
              </div>
              
              {/* Scalable card */}
              <div className="highlight-card">
                <div className="highlight-icon">
                  <i className="fas fa-rocket"></i>
                </div>
                <div className="highlight-text">
                  <h3>Scalable</h3>
                  <p>Enterprise-grade infrastructure that grows with you</p>
                </div>
              </div>
              
              {/* Secure card */}
              <div className="highlight-card">
                <div className="highlight-icon">
                  <i className="fas fa-shield-alt"></i>
                </div>
                <div className="highlight-text">
                  <h3>Secure</h3>
                  <p>Industry-leading security standards and practices</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {isMobile && (
          <div className="scroll-indicator">
            <i className="fas fa-chevron-down"></i>
          </div>
        )}
      </section>

      <style jsx>{`
        #hero {
          position: relative;
          min-height: 100vh;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          padding: 4rem 0;
        }

        .parallax, #three-canvas {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 0;
        }

        .container {
          width: 100%;
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 1.5rem;
          position: relative;
          z-index: 10;
        }

        .hero-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          width: 100%;
        }

        .hero-text {
          max-width: 100%;
          width: 100%;
          margin: 0 auto 2rem;
          position: relative;
        }

        /* Force white text in hero section for dark mode */
        .glitch-effect {
          font-size: 4rem;
          font-weight: 700;
          margin-bottom: 1rem;
          color: #FFFFFF !important;
          text-shadow: 0 0 10px rgba(0, 0, 0, 0.7), 0 0 20px rgba(0, 0, 0, 0.5);
        }

        .typewriter {
          font-size: 1.8rem;
          margin-bottom: 2rem;
          color: #FFFFFF !important;
          text-shadow: 0 0 10px rgba(0, 0, 0, 0.7);
          display: inline-block;
          width: auto;
          min-width: 100%;
          white-space: normal;
          overflow: visible;
        }

        /* Create a dark semi-transparent background for better text visibility */
        .hero-text:before {
          content: '';
          position: absolute;
          top: -20px;
          left: -20px;
          right: -20px;
          bottom: -20px;
          background: rgba(0, 0, 0, 0.4);
          border-radius: 1rem;
          z-index: -1;
        }

        .highlight-card {
          background: rgba(245, 245, 250, 0.7);
          backdrop-filter: blur(10px);
          border-radius: 0.8rem;
          padding: 2rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          transition: all 0.3s ease;
          border: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* Dark mode card style */
        :global(body.dark) .highlight-card, 
        :global(html.dark) .highlight-card {
          background: rgba(30, 30, 45, 0.7);
          border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .highlight-text h3 {
          font-size: 1.8rem;
          font-weight: 600;
          margin-bottom: 0.75rem;
          color: #1a1a2a;
        }

        .highlight-text p {
          font-size: 1.1rem;
          color: #4a4a57;
          line-height: 1.4;
        }

        /* Force light text in cards for dark mode */
        :global(body.dark) .highlight-text h3, 
        :global(html.dark) .highlight-text h3 {
          color: #FFFFFF !important;
        }

        :global(body.dark) .highlight-text p, 
        :global(html.dark) .highlight-text p {
          color: rgba(255, 255, 255, 0.7) !important;
        }

        .hero-cta {
          display: flex;
          margin: 1.5rem 0 3rem;
          justify-content: center;
        }

        .cta-button {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          padding: 1rem 2.5rem;
          border-radius: 9999px;
          font-size: 1.2rem;
          font-weight: 600;
          transition: all 0.3s ease;
        }

        .cta-button.primary {
          background: rgb(77, 96, 232);
          color: white;
          box-shadow: 0 4px 15px rgba(77, 96, 232, 0.3);
        }

        .hero-highlights {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 2rem;
          width: 100%;
          max-width: 1200px;
          margin: 0 auto;
        }

        .highlight-icon {
          width: 5rem;
          height: 5rem;
          border-radius: 50%;
          background: rgba(77, 96, 232, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 2rem;
          color: rgb(77, 96, 232);
          margin-bottom: 1.5rem;
        }

        .scroll-indicator {
          position: absolute;
          bottom: 2rem;
          left: 50%;
          transform: translateX(-50%);
          animation: bounce 2s infinite;
          opacity: 0.7;
          font-size: 1.5rem;
          color: #FFFFFF;
        }

        /* Override light mode styles when not in dark mode */
        @media (prefers-color-scheme: light) {
          :global(body:not(.dark)) .glitch-effect, 
          :global(html:not(.dark)) .glitch-effect {
            color: #1a1a2a !important;
            text-shadow: none;
          }
          
          :global(body:not(.dark)) .typewriter, 
          :global(html:not(.dark)) .typewriter {
            color: #333 !important;
            text-shadow: none;
          }
          
          :global(body:not(.dark)) .hero-text:before, 
          :global(html:not(.dark)) .hero-text:before {
            background: transparent;
          }
        }

        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0) translateX(-50%);
          }
          40% {
            transform: translateY(-20px) translateX(-50%);
          }
          60% {
            transform: translateY(-10px) translateX(-50%);
          }
        }

        /* Mobile styles - these will override desktop styles */
        @media (max-width: 768px) {
          #hero {
            min-height: auto;
            height: auto;
            padding: 5rem 0 2rem;
            width: 100vw;
            max-width: 100%;
            overflow-x: hidden;
          }
          
          .container {
            padding: 0 1rem;
          }
          
          .hero-text {
            max-width: 100%;
            padding: 0 1rem;
          }
          
          .glitch-effect {
            font-size: 2.8rem;
          }
          
          .typewriter {
            font-size: 1.4rem;
            white-space: normal;
            border-right: none;
            width: 100%;
            display: block;
          }
          
          .hero-highlights {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            width: 100%;
            max-width: 90%;
            margin: 1rem auto 0;
          }
          
          .highlight-card {
            width: 100%;
            margin-bottom: 1rem;
            padding: 1.2rem;
            flex-direction: row;
            align-items: center;
            gap: 1rem;
            text-align: left;
          }
          
          .highlight-icon {
            width: 3.5rem;
            height: 3.5rem;
            font-size: 1.5rem;
            margin-bottom: 0;
            min-width: 3rem;
            min-height: 3rem;
          }
          
          .highlight-text h3 {
            font-size: 1.4rem;
            margin-bottom: 0.3rem;
          }
          
          .highlight-text p {
            font-size: 0.9rem;
            line-height: 1.3;
          }
          
          .hero-cta {
            width: 100%;
            justify-content: center;
            margin: 1.5rem 0 2rem;
          }
          
          .cta-button {
            width: 100%;
            max-width: 280px;
            font-size: 1.1rem;
            padding: 0.9rem 1.8rem;
          }

          #hero::after {
            content: '';
            display: block;
            height: 2rem;
            width: 100%;
            clear: both;
          }
        }
        
        /* Very small devices */
        @media (max-width: 400px) {
          .glitch-effect {
            font-size: 2.4rem;
          }
          
          .highlight-card {
            padding: 1rem 1.2rem;
          }
          
          .highlight-icon {
            min-width: 2.8rem;
            min-height: 2.8rem;
            font-size: 1.2rem;
          }
          
          .highlight-text h3 {
            font-size: 1.3rem;
          }
          
          .cta-button {
            max-width: 90%;
          }
        }
      `}</style>
    </>
  );
}