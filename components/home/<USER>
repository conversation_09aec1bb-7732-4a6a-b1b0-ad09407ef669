import ContactForm from "@/components/contact/ContactForm"
import Link from "next/link"

export default function ContactPreview() {
  return (
    <section id="contact-preview" className="section">
      <div className="container">
        <div className="contact-content">
          <div className="contact-info">
            <span className="section-subtitle">Get In Touch</span>
            <h2 className="section-title">Ready to Transform Your Business?</h2>
            <p>Partner with Qoverse to transform your ideas into reality—reach out today for a consultation.</p>
            <div className="contact-methods">
              <div className="contact-method">
                <div className="method-icon">
                  <i className="fas fa-map-marker-alt"></i>
                </div>
                <div className="method-details">
                  <h4>Address</h4>
                  <p>Estonia, Harju maakond, Kuusalu vald, Pudisoo k<PERSON>la, Männimäe/1, 74626</p>
                </div>
              </div>
              {/* <div className="contact-method"> */}
                {/* <div className="method-icon"> */}
                  {/* <i className="fas fa-phone-alt"></i> */}
                {/* </div> */}
                {/* <div className="method-details">
                  <h4>Phone</h4>
                  <p>
                    <a href="tel:+***********">+****************</a>
                  </p>
                </div> */}
              {/* </div> */}
              <div className="contact-method">
                <div className="method-icon">
                  <i className="fas fa-envelope"></i>
                </div>
                <div className="method-details">
                  <h4>Email</h4>
                  <p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                  </p>
                </div>
              </div>
            </div>
            <div className="social-links">
              <a
                href="https://www.linkedin.com/company/qoverse"
                target="_blank"
                rel="noopener noreferrer"
                className="social-link"
              >
                <i className="fab fa-linkedin"></i>
              </a>
              <a 
                href="https://www.x.com/qoverse" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="social-link"
              >
                <i className="fab fa-twitter"></i>
              </a>
               {/* <a
                href="#"
                className="social-link"
              >
                <i className="fab fa-facebook"></i>
              </a>
              <a
                href="#"
                className="social-link"
              >
                <i className="fab fa-instagram"></i>
              </a> */}
            </div>
            <div className="contact-preview-cta">
              <Link href="/contact" className="cta-button primary">
                <span className="button-text">Contact Us</span>
                <span className="button-icon">→</span>
              </Link>
            </div>
          </div> 
          <div className="contact-form-container">
            <ContactForm />
          </div>
        </div>
      </div>
    </section>
  )
}
