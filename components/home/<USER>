'use client'

import { useState, useEffect, useRef } from 'react'

export default function VideoModal() {
  const [isActive, setIsActive] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    const showreelButton = document.querySelector('.play-showreel')
    if (showreelButton) {
      showreelButton.addEventListener('click', (e) => {
        e.preventDefault()
        setIsActive(true)
        document.body.style.overflow = 'hidden'
        if (videoRef.current) {
          setTimeout(() => {
            videoRef.current!.currentTime = 0
            videoRef.current!.play().catch((error) => {
              console.error('Video play error:', error)
            })
          }, 100)
        }
      })
    }

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isActive) {
        closeModal()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => {
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isActive])

  const closeModal = () => {
    setIsActive(false)
    document.body.style.overflow = ''
    if (videoRef.current) {
      videoRef.current.pause()
      videoRef.current.currentTime = 0
    }
  }

  return (
    <div id="showreel-modal" className={`modal ${isActive ? 'active' : ''}`}>
      <div className="modal-overlay" onClick={closeModal}></div>
      <div className="modal-container">
        <div className="modal-close" onClick={closeModal}>
          ×
        </div>
        <div className="modal-content">
          <div className="video-container">
            <video id="showreel-video" controls muted ref={videoRef}>
              <source src="/assets/showreel.mp4" type="video/mp4" />
              Your browser does not support the video tag.{' '}
              <a href="/assets/showreel.mp4" download>
                Download the video
              </a>
              .
            </video>
          </div>
        </div>
      </div>
    </div>
  )
}