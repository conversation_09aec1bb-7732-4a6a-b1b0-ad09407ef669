// lib/animations.js

declare global {
  interface Window {
    gsap: {
      utils: {
        toArray: <T>(selector: string | NodeList | Array<any>) => T[];
      };
      registerPlugin: (plugin: any) => void;
      from: (target: any, vars: any) => void;
    };
    ScrollTrigger: any;
  }
}

export function initAnimations() {
  // Initialize cursor effects if they exist
  initCursorEffects();
  
  // Initialize scroll animations if GSAP exists
  if (typeof window !== 'undefined' && window.gsap) {
    initScrollAnimations();
  }
  
  // Initialize hover effects
  initHoverEffects();
  
  // Hide preloader if it exists
  hidePreloader();
}

function initCursorEffects() {
  if (typeof window === 'undefined') return;
  
  const cursor = document.querySelector('.cursor-dot') as HTMLElement;
  const cursorOutline = document.querySelector('.cursor-outline') as HTMLElement;
  
  if (!cursor || !cursorOutline) return;
  
  // Make cursor visible
  cursor.style.opacity = '1';
  cursorOutline.style.opacity = '1';
  
  document.addEventListener('mousemove', (e) => {
    cursor.style.transform = `translate(${e.clientX}px, ${e.clientY}px)`;
    cursorOutline.style.transform = `translate(${e.clientX}px, ${e.clientY}px)`;
  });
  
  // Hover effects
  const links = document.querySelectorAll('a, button, .info-card, .faq-item');
  links.forEach(link => {
    link.addEventListener('mouseenter', () => {
      cursor.style.transform = 'translate(-50%, -50%) scale(0.5)';
      cursorOutline.style.transform = 'translate(-50%, -50%) scale(1.5)';
      cursorOutline.style.border = '2px solid rgba(65, 105, 225, 0.8)';
    });
    
    link.addEventListener('mouseleave', () => {
      cursor.style.transform = 'translate(-50%, -50%) scale(1)';
      cursorOutline.style.transform = 'translate(-50%, -50%) scale(1)';
      cursorOutline.style.border = '2px solid rgba(65, 105, 225, 0.5)';
    });
  });
}

function initScrollAnimations() {
  if (typeof window === 'undefined') return;
  
  // Check if gsap and ScrollTrigger are available
  if (!window.gsap || !window.ScrollTrigger) return;
  
  const gsap = window.gsap;
  const ScrollTrigger = window.ScrollTrigger;
  
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);
  
  // Animate section headers on scroll
  gsap.utils.toArray<HTMLElement>('.section-header').forEach(header => {
    gsap.from(header, {
      y: 50,
      opacity: 0,
      duration: 0.8,
      scrollTrigger: {
        trigger: header,
        start: 'top 80%',
        toggleActions: 'play none none none'
      }
    });
  });
  
  // Animate contact info cards
  gsap.utils.toArray('.info-card').forEach((card, index) => {
    gsap.from(card, {
      y: 30,
      opacity: 0,
      duration: 0.6,
      delay: index * 0.1,
      scrollTrigger: {
        trigger: '.contact-info-cards',
        start: 'top 80%',
        toggleActions: 'play none none none'
      }
    });
  });
  
  // Animate form
  gsap.from('.contact-form-container', {
    x: 50,
    opacity: 0,
    duration: 0.8,
    scrollTrigger: {
      trigger: '.contact-form-container',
      start: 'top 80%',
      toggleActions: 'play none none none'
    }
  });
  
  // Animate FAQ items
  gsap.utils.toArray('.faq-item').forEach((item, index) => {
    gsap.from(item, {
      y: 30,
      opacity: 0,
      duration: 0.6,
      delay: index * 0.1,
      scrollTrigger: {
        trigger: '.faq-container',
        start: 'top 80%',
        toggleActions: 'play none none none'
      }
    });
  });
}

function initHoverEffects() {
  if (typeof window === 'undefined') return;
  
  // Add hover sound effects
  const hoverElements = document.querySelectorAll('a, button, .info-card, .faq-item');
  const hoverSound = document.getElementById('hover-sound') as HTMLAudioElement;
  
  if (!hoverSound) return;
  
  hoverElements.forEach(element => {
    element.addEventListener('mouseenter', () => {
      if (window.audioEnabled) {
        hoverSound.currentTime = 0;
        hoverSound.play().catch(e => {});
      }
    });
  });
}

function hidePreloader() {
  if (typeof window === 'undefined') return;
  
  const preloader = document.getElementById('preloader');
  
  if (preloader) {
    setTimeout(() => {
      preloader.style.opacity = '0';
      setTimeout(() => {
        preloader.style.display = 'none';
      }, 500);
    }, 500);
  }
}