/* Map Styles - Add to your existing CSS or import as needed */
.contact-map {
    position: relative;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    height: 30rem;
    margin-bottom: 4rem;
  }
  
  .map-iframe {
    width: 100%;
    height: 100%;
    border: none;
    filter: grayscale(0.7) contrast(1.2);
    transition: all 0.5s ease;
  }
  
  .contact-map:hover .map-iframe {
    filter: grayscale(0) contrast(1);
  }
  
  .map-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(65, 105, 225, 0.2), transparent);
    pointer-events: none;
    z-index: 1;
  }
  
  .map-pin {
    position: absolute;
    bottom: 1.5rem;
    right: 1.5rem;
    background: var(--accent-color);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 2rem;
    font-size: 1.4rem;
    font-weight: 600;
    z-index: 2;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.8rem;
    transition: transform 0.3s ease;
  }
  
  .map-pin:hover {
    transform: translateY(-5px);
  }
  
  .map-pin i {
    font-size: 1.6rem;
  }
  
  /* Media Queries */
  @media (max-width: 768px) {
    .contact-map {
      height: 25rem;
    }
  }
  
  @media (max-width: 576px) {
    .contact-map {
      height: 20rem;
    }
    
    .map-pin {
      padding: 0.8rem 1.2rem;
      font-size: 1.2rem;
    }
  }