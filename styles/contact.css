/* Contact Grid Layout */
.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

@media (max-width: 992px) {
  .contact-grid {
    grid-template-columns: 1fr;
  }
}

/* Map Styles */
.contact-map {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  height: 32rem;
  margin-bottom: 2.5rem;
}

.map-iframe {
  width: 100%;
  height: 100%;
  border: none;
  filter: grayscale(0.7) contrast(1.2);
  transition: all 0.5s ease;
}

.contact-map:hover .map-iframe {
  filter: grayscale(0) contrast(1);
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(65, 105, 225, 0.2), transparent);
  pointer-events: none;
  z-index: 1;
}

.map-pin {
  position: absolute;
  bottom: 1.5rem;
  right: 1.5rem;
  background: var(--accent-color);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 2rem;
  font-size: 1.4rem;
  font-weight: 600;
  z-index: 2;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.8rem;
  transition: transform 0.3s ease;
}

.map-pin:hover {
  transform: translateY(-5px);
}

.map-pin i {
  font-size: 1.6rem;
}

/* Contact Info Cards */
.contact-info-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-bottom: 2.5rem;
}

.info-card {
  background: var(--card-bg);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.info-icon {
  width: 4.5rem;
  height: 4.5rem;
  border-radius: 50%;
  background: rgba(65, 105, 225, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: var(--accent-color);
  flex-shrink: 0;
}

.info-content h3 {
  font-size: 1.8rem;
  margin-bottom: 0.8rem;
}

.info-content p {
  font-size: 1.5rem;
  margin: 0;
  line-height: 1.5;
}

.info-content a {
  color: var(--text-color);
  transition: color 0.3s ease;
}

.info-content a:hover {
  color: var(--accent-color);
}

/* Company Info */
.company-info {
  background: var(--card-bg);
  border-radius: 1rem;
  padding: 3rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.company-info h3 {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
}

.company-info p {
  font-size: 1.5rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.company-info .social-links {
  display: flex;
  gap: 1.5rem;
  margin-top: 2rem;
}

.company-info .social-link {
  width: 4.5rem;
  height: 4.5rem;
  border-radius: 50%;
  background: rgba(65, 105, 225, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: var(--accent-color);
  transition: all 0.3s ease;
}

.company-info .social-link:hover {
  background: var(--accent-color);
  color: white;
  transform: translateY(-5px);
}

/* FAQ Accordion Styles */
.faq-item {
  background: var(--card-bg);
  border-radius: 1rem;
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.faq-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.faq-question {
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.faq-question h3 {
  font-size: 1.8rem;
  margin-bottom: 0;
  transition: color 0.3s ease;
}

.faq-item:hover .faq-question h3 {
  color: var(--accent-color);
}

.question-icon {
  width: 3rem;
  height: 3rem;
  background: rgba(65, 105, 225, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  color: var(--accent-color);
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-left: 1.5rem;
}

.faq-item.active .question-icon {
  transform: rotate(45deg);
  background: var(--accent-color);
  color: white;
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  padding: 0 2rem;
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.faq-item.active .faq-answer {
  max-height: 500px;
  padding: 0 2rem 2rem;
}

.faq-answer p {
  margin: 0;
  font-size: 1.5rem;
  line-height: 1.7;
}

/* Form Validation Styles */
.form-group.error input,
.form-group.error textarea,
.form-group.error select {
  border-color: var(--error-color);
  box-shadow: 0 0 0 2px rgba(239, 71, 111, 0.2);
}

.form-validation {
  color: var(--error-color);
  font-size: 1.2rem;
  min-height: 1.8rem;
  margin-top: 0.5rem;
}

.form-status {
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 0.8rem;
  font-size: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  min-height: 0;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
}

.form-status.success,
.form-status.error,
.form-status.info {
  min-height: 6rem;
  max-height: 10rem;
  opacity: 1;
}

.form-status.success {
  background: rgba(15, 224, 106, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(15, 224, 106, 0.2);
}

.form-status.error {
  background: rgba(239, 71, 111, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(239, 71, 111, 0.2);
}

.form-status.info {
  background: rgba(65, 105, 225, 0.1);
  color: var(--accent-color);
  border: 1px solid rgba(65, 105, 225, 0.2);
}

/* Form Loading State */
.form-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Form Focus Styles */
.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.2);
}

/* Contact Form Container */
.contact-form-container {
  background: var(--card-bg);
  border-radius: 1.5rem;
  padding: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-header {
  margin-bottom: 1.5rem;
}

.form-header h3 {
  font-size: 2.2rem;
  margin-bottom: 1rem;
}

.form-header p {
  font-size: 1.5rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

.form-group {
  position: relative;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.8rem;
  background: var(--input-bg);
  color: var(--text-color);
  font-size: 1.6rem;
  transition: all 0.3s ease;
}

.form-group textarea {
  min-height: 15rem;
  resize: vertical;
}

/* Media Queries */
@media (max-width: 768px) {
  .contact-info-cards {
    grid-template-columns: 1fr;
  }
  
  .contact-form-container {
    padding: 2rem;
  }
}