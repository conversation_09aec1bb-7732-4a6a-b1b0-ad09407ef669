@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
/* Add this at the end of your globals.css file */

/* Import page-specific styles */
@import './contact.css';
/* Improved Typewriter Effect */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--accent-color);
  white-space: nowrap;
  margin: 0 auto;
  letter-spacing: 0.05em;
  width: 100%; /* Ensure the container is full width */
  animation: 
    typing 3.5s steps(60, end), /* Increased steps for smoother animation */
    blink-caret 0.75s step-end infinite;
  animation-delay: 0.5s; /* Reduced delay */
  animation-fill-mode: both;
  font-size: 2.2rem;
  max-width: 60rem;
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: var(--accent-color) }
}

/* For mobile screens, adjust the animation */
@media (max-width: 768px) {
  .typewriter {
    white-space: normal;
    animation: none;
    border-right: none;
    display: block;
    width: 100%;
  }
}
/* Complete mobile layout fix for hero section */
@media (max-width: 768px) {
  /* Core viewport fixes */
  body, html {
    width: 100%;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
  }
  
  /* Containment fixes */
  #hero {
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 5rem 0 2rem;
    overflow: hidden;
    position: relative;
    min-height: auto;
    height: auto;
  }
  
  /* Background element fixes */
  .parallax, #three-canvas {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  
  /* Container fixes */
  .container {
    width: 100%;
    padding: 0 1rem;
    margin: 0 auto;
    box-sizing: border-box;
  }
  
  /* Content spacing */
  .hero-content {
    margin-bottom: 0;
    padding-bottom: 1rem;
  }
  
  /* Cards adjustments */
  .hero-highlights {
    width: 100%;
    margin: 1rem auto 0;
    padding: 0 0.5rem;
  }
  
  .highlight-card {
    width: 100%;
    margin-bottom: 1rem;
    box-sizing: border-box;
  }
  
  /* Fix for sections appearing too early */
  #services-preview {
    margin-top: 1rem;
    padding-top: 3rem;
    position: relative;
  }
  
  /* Fix for WHAT WE OFFER appearing in hero */
  .section-subtitle {
    display: block;
    margin-top: 3rem;
  }
  
  /* Add clear separation between hero and next section */
  #hero::after {
    content: '';
    display: block;
    height: 2rem;
    width: 100%;
    clear: both;
  }
}

/* Additional tablet-specific fixes */
@media (min-width: 600px) and (max-width: 768px) {
  #hero {
    padding: 6rem 0 3rem;
  }
  
  .hero-highlights {
    max-width: 90%;
  }
}
/* Fix for dropdown issues */
@media (min-width: 768px) {
  /* On desktop, prevent the dropdown from closing immediately on hover out */
  .dropdown .dropdown-wrapper {
    transition-delay: 0s;
  }
  
  .dropdown:hover .dropdown-wrapper {
    transition-delay: 0s;
  }
  
  /* Keep dropdown visible until hover leaves both dropdown and its content */
  .dropdown-wrapper:hover,
  .dropdown:hover .dropdown-wrapper {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
  }
}

/* Mobile dropdown enhancements */
@media (max-width: 767px) {
  /* Override hover effects on mobile */
  .dropdown:hover .dropdown-wrapper {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(1rem);
  }
  
  /* Only show when active class is present */
  .dropdown.active .dropdown-wrapper {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    position: static;
    width: 100%;
    padding: 1rem 0;
    background: transparent;
    box-shadow: none;
    border: none;
    transform: none;
    max-height: 500px;
    overflow-y: auto;
  }
  
  /* Adjust dropdown content layout */
  .dropdown-content {
    grid-template-columns: 1fr;
    padding: 0 1rem;
  }
  
  .dropdown-link {
    padding: 1rem;
    border-left: 2px solid transparent;
  }
  
  .dropdown-link:hover {
    transform: translateX(5px);
  }
}

/* FAQ Accordion Styles */
.faq-item {
  background: var(--secondary-bg);
  border-radius: 1rem;
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.faq-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.faq-question {
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faq-question h3 {
  font-size: 1.8rem;
  margin-bottom: 0;
  transition: color 0.3s ease;
}

.faq-item:hover .faq-question h3 {
  color: var(--accent-color);
}

.question-icon {
  width: 3rem;
  height: 3rem;
  background: rgba(65, 105, 225, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  color: var(--accent-color);
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-left: 1.5rem;
}

.faq-item.active .question-icon {
  transform: rotate(45deg);
  background: var(--accent-color);
  color: white;
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  padding: 0 2rem;
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.faq-item.active .faq-answer {
  max-height: 500px;
  padding: 0 2rem 2rem;
}

.faq-answer p {
  margin: 0;
  font-size: 1.5rem;
  line-height: 1.7;
}

.faq-container {
  max-width: 80rem;
  margin: 0 auto;
}
/* Add these Contact Page styles to your existing globals.css */

/* Contact Grid Layout */
.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

@media (max-width: 992px) {
  .contact-grid {
    grid-template-columns: 1fr;
  }
}

/* Map Styles */
.contact-map {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  height: 32rem;
  margin-bottom: 2.5rem;
}

.map-iframe {
  width: 100%;
  height: 100%;
  border: none;
  filter: grayscale(0.7) contrast(1.2);
  transition: all 0.5s ease;
}

.contact-map:hover .map-iframe {
  filter: grayscale(0) contrast(1);
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(65, 105, 225, 0.2), transparent);
  pointer-events: none;
  z-index: 1;
}

.map-pin {
  position: absolute;
  bottom: 1.5rem;
  right: 1.5rem;
  background: var(--accent-color, #4169e1);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 2rem;
  font-size: 1.4rem;
  font-weight: 600;
  z-index: 2;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.8rem;
  transition: transform 0.3s ease;
}

.map-pin:hover {
  transform: translateY(-5px);
}

.map-pin i {
  font-size: 1.6rem;
}

/* Contact Info Cards */
.contact-info-container {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

.contact-info-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.info-card {
  background: var(--card-bg, #1a1a2a);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.info-icon {
  width: 4.5rem;
  height: 4.5rem;
  border-radius: 50%;
  background: rgba(65, 105, 225, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: var(--accent-color, #4169e1);
  flex-shrink: 0;
}

.info-content h3 {
  font-size: 1.8rem;
  margin-bottom: 0.8rem;
}

.info-content p {
  font-size: 1.5rem;
  margin: 0;
  line-height: 1.5;
}

.info-content a {
  color: var(--text-color, #e6e6fa);
  transition: color 0.3s ease;
}

.info-content a:hover {
  color: var(--accent-color, #4169e1);
}

/* Company Info */
.company-info {
  background: var(--card-bg, #1a1a2a);
  border-radius: 1rem;
  padding: 3rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.company-info h3 {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
}

.company-info p {
  font-size: 1.5rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.company-info .social-links {
  display: flex;
  gap: 1.5rem;
  margin-top: 2rem;
}

.company-info .social-link {
  width: 4.5rem;
  height: 4.5rem;
  border-radius: 50%;
  background: rgba(65, 105, 225, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: var(--accent-color, #4169e1);
  transition: all 0.3s ease;
}

.company-info .social-link:hover {
  background: var(--accent-color, #4169e1);
  color: white;
  transform: translateY(-5px);
}

/* Form Validation Styles */
.form-group.error input,
.form-group.error textarea,
.form-group.error select {
  border-color: var(--error-color, #ef476f);
  box-shadow: 0 0 0 2px rgba(239, 71, 111, 0.2);
}

.form-validation {
  color: var(--error-color, #ef476f);
  font-size: 1.2rem;
  min-height: 1.8rem;
  margin-top: 0.5rem;
}

.form-status {
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 0.8rem;
  font-size: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  min-height: 0;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
}

.form-status.success,
.form-status.error,
.form-status.info {
  min-height: 6rem;
  max-height: 10rem;
  opacity: 1;
}

.form-status.success {
  background: rgba(15, 224, 106, 0.1);
  color: var(--success-color, #0fe06a);
  border: 1px solid rgba(15, 224, 106, 0.2);
}

.form-status.error {
  background: rgba(239, 71, 111, 0.1);
  color: var(--error-color, #ef476f);
  border: 1px solid rgba(239, 71, 111, 0.2);
}

.form-status.info {
  background: rgba(65, 105, 225, 0.1);
  color: var(--accent-color, #4169e1);
  border: 1px solid rgba(65, 105, 225, 0.2);
}

/* Form Loading State */
.form-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Form Focus Styles */
.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--accent-color, #4169e1);
  box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.2);
}

/* Contact Form Container */
.contact-form-container {
  background: var(--card-bg, #1a1a2a);
  border-radius: 1.5rem;
  padding: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-header {
  margin-bottom: 1.5rem;
}

.form-header h3 {
  font-size: 2.2rem;
  margin-bottom: 1rem;
}

.form-header p {
  font-size: 1.5rem;
  line-height: 1.6;
  color: var(--text-secondary, #a0a0b0);
}

.form-group {
  position: relative;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 1.5rem;
  border: 1px solid var(--border-color, #2d2d42);
  border-radius: 0.8rem;
  background: var(--input-bg, #242436);
  color: var(--text-color, #e6e6fa);
  font-size: 1.6rem;
  transition: all 0.3s ease;
}

.form-group textarea {
  min-height: 15rem;
  resize: vertical;
}

/* Media Queries */
@media (max-width: 768px) {
  .contact-info-cards {
    grid-template-columns: 1fr;
  }
  
  .contact-form-container {
    padding: 2rem;
  }
}

/* Add these styles to your globals.css file */

/* Form Validation Styles */
.form-group.error input,
.form-group.error textarea,
.form-group.error select {
  border-color: var(--error-color, #ef476f);
  box-shadow: 0 0 0 2px rgba(239, 71, 111, 0.2);
}

.form-validation {
  color: var(--error-color, #ef476f);
  font-size: 1.2rem;
  min-height: 1.8rem;
  margin-top: 0.5rem;
  transition: all 0.3s ease;
}

.form-status {
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 0.8rem;
  font-size: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  min-height: 0;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
}

.form-status.success,
.form-status.error,
.form-status.info {
  min-height: 6rem;
  max-height: 10rem;
  opacity: 1;
}

.form-status.success {
  background: rgba(15, 224, 106, 0.1);
  color: var(--success-color, #0fe06a);
  border: 1px solid rgba(15, 224, 106, 0.2);
}

.form-status.error {
  background: rgba(239, 71, 111, 0.1);
  color: var(--error-color, #ef476f);
  border: 1px solid rgba(239, 71, 111, 0.2);
}

.form-status.info {
  background: rgba(65, 105, 225, 0.1);
  color: var(--accent-color, #4169e1);
  border: 1px solid rgba(65, 105, 225, 0.2);
}

/* Form Loading State */
.form-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Form Focus Styles */
.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--accent-color, #4169e1);
  box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.2);
}

/* Contact Grid Layout */
.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

@media (max-width: 992px) {
  .contact-grid {
    grid-template-columns: 1fr;
  }
}

/* Interactive Map Styles */
.contact-map {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: var(--shadow-lg, 0 8px 24px rgba(0, 0, 0, 0.3));
  height: 32rem;
  margin-bottom: 2.5rem;
}

.map-iframe {
  width: 100%;
  height: 100%;
  border: none;
  filter: grayscale(0.7) contrast(1.2);
  transition: all 0.5s ease;
}

.contact-map:hover .map-iframe {
  filter: grayscale(0) contrast(1);
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(65, 105, 225, 0.2), transparent);
  pointer-events: none;
  z-index: 1;
}

.map-pin {
  position: absolute;
  bottom: 1.5rem;
  right: 1.5rem;
  background: var(--accent-color, #4169e1);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 2rem;
  font-size: 1.4rem;
  font-weight: 600;
  z-index: 2;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.8rem;
  transition: transform 0.3s ease;
}

.map-pin:hover {
  transform: translateY(-5px);
}

.map-pin i {
  font-size: 1.6rem;
}

/* Contact Info Cards */
.contact-info-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-bottom: 2.5rem;
}

@media (max-width: 768px) {
  .contact-info-cards {
    grid-template-columns: 1fr;
  }
}

.info-card {
  background: var(--secondary-bg, #0f0f1e);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.25));
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg, 0 8px 24px rgba(0, 0, 0, 0.3));
}

.info-icon {
  width: 5rem;
  height: 5rem;
  background: rgba(65, 105, 225, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--accent-color, #4169e1);
  flex-shrink: 0;
}

.info-content h3 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.info-content p {
  font-size: 1.5rem;
  margin-bottom: 0;
}

.info-content a {
  color: var(--text-secondary, #b0b0c0);
  transition: color 0.3s ease;
}

.info-content a:hover {
  color: var(--accent-color, #4169e1);
}

/* Company Info */
.company-info {
  background: var(--secondary-bg, #0f0f1e);
  border-radius: 1rem;
  padding: 3rem;
  box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.25));
}

.company-info h3 {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
}

.company-info p {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.social-links {
  display: flex;
  gap: 1.5rem;
  margin-top: 2rem;
}

.social-link {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: var(--tertiary-bg, #141428);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: var(--text-primary, #ffffff);
  transition: background 0.3s ease, transform 0.3s ease, color 0.3s ease;
}

.social-link:hover {
  background: var(--accent-color, #4169e1);
  color: white;
  transform: translateY(-5px);
}

/* Contact Form Container */
.contact-form-container {
  background: var(--secondary-bg, #0f0f1e);
  border-radius: 1rem;
  padding: 4rem;
  box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.25));
}

@media (max-width: 768px) {
  .contact-form-container {
    padding: 2rem;
  }
}

.form-header {
  margin-bottom: 3rem;
}

.form-header h3 {
  font-size: 2.4rem;
  margin-bottom: 1rem;
}

.form-header p {
  font-size: 1.6rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-group {
  position: relative;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 1.5rem 2rem;
  border: 1px solid rgba(65, 105, 225, 0.2);
  border-radius: 0.8rem;
  background: var(--tertiary-bg, #141428);
  font-size: 1.6rem;
  color: var(--text-primary, #ffffff);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group textarea {
  min-height: 15rem;
  resize: vertical;
}

.form-group select {
  appearance: none;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234169e1'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 2rem;
}

/* Sound Toggle and Back To Top */
.sound-toggle {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  width: 4rem;
  height: 4rem;
  background: var(--secondary-bg, #0f0f1e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 100;
  box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.25));
  transition: transform 0.3s ease;
}

.sound-toggle:hover {
  transform: scale(1.1);
}

.sound-icon {
  position: absolute;
  font-size: 1.8rem;
  color: var(--text-primary, #ffffff);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.sound-icon.sound-on {
  opacity: 0;
  transform: scale(0);
}

.sound-icon.sound-off {
  opacity: 1;
  transform: scale(1);
}

.sound-icon.active {
  opacity: 1;
  transform: scale(1);
}

.sound-icon.sound-off.active {
  opacity: 0;
  transform: scale(0);
}

.back-to-top {
  position: fixed;
  bottom: -5rem;
  right: 2rem;
  width: 4rem;
  height: 4rem;
  background: var(--secondary-bg, #0f0f1e);
  border-radius: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: var(--text-primary, #ffffff);
  cursor: pointer;
  z-index: 99;
  transition: bottom 0.3s ease, background 0.3s ease;
  box-shadow: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.25));
}

.back-to-top.visible {
  bottom: 9rem;
}

.back-to-top:hover {
  background: var(--accent-color, #4169e1);
  color: white;
}
