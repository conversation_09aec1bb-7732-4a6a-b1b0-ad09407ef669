"use client"

import { useEffect } from "react"
import Link from "next/link"
import { initAnimations } from "@/lib/animations"

export default function AboutPage() {
  useEffect(() => {
    // Initialize animations after component mounts
    const timer = setTimeout(() => {
      initAnimations()
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <>
      <section id="hero">
        <div className="parallax"></div>
        <div id="three-canvas"></div>
        <div className="hero-content container">
          <div className="hero-text">
            <h1 className="glitch-effect" data-text="About Qoverse">
              About Qoverse
            </h1>
            <p className="typewriter">Learn about the vision and expertise driving our technological innovation.</p>
          </div>
          {/* <div className="scroll-indicator">
            <div className="mouse">
              <div className="mouse-wheel"></div>
            </div>
            <div className="scroll-text">Scroll Down</div>
          </div> */}
        </div>
      </section>

      <section id="about-main" className="section">
        <div className="section-pattern"></div>
        <div className="container">
          <div className="section-header">
            <span className="section-subtitle">Our Journey</span>
            <h2 className="section-title">Our Story</h2>
            <p className="section-description">
              Founded in 2024 and headquartered in Kuusalu Vald, Estonia, Qoverse is a leading software service provider with
              a team of exceptional experts.
            </p>
          </div>
          <div className="about-story">
            <div className="story-content">
              <p>
                At Qoverse, we specialize in AI solutions, machine learning optimization, web development, data
                analytics, and more, delivering transformative technology to empower our clients. Our mission is to push
                the boundaries of innovation, exceeding expectations with every project.
              </p>
              <div className="story-stats">
                <div className="stat-item">
                  <span className="stat-number" data-count="2020">
                    5
                  </span>
                  <span className="stat-label">Founded</span>
                </div>
                <div className="stat-item">
                  <span className="stat-number" data-count="50">
                    16
                  </span>
                  <span className="stat-symbol">+</span>
                  <span className="stat-label">Experts</span>
                </div>
                <div className="stat-item">
                  <span className="stat-number" data-count="100">
                    30
                  </span>
                  <span className="stat-symbol">+</span>
                  <span className="stat-label">Projects</span>
                </div>
              </div>
              <div className="story-quote">
                <blockquote>
                  "Our vision is to harness the power of advanced technology to solve complex challenges and drive
                  digital transformation for businesses worldwide."
                </blockquote>
                <cite>— Rizwan Hassan, CEO</cite>
              </div>
            </div>
            <div className="story-image">
              <img src="/assets/office.jpg" alt="Qoverse Office" />
              <div className="image-overlay"></div>
            </div>
          </div>
        </div>
      </section>

      <section id="team" className="section">
        <div className="container">
          <div className="section-header">
            <span className="section-subtitle">Meet Our Experts</span>
            <h2 className="section-title">Leadership Team</h2>
            <p className="section-description">
              Our team of visionaries and technology experts is dedicated to delivering exceptional solutions.
            </p>
          </div>
          <div className="team-showcase">
            <div className="team-member-card">
              <div className="member-image">
                <img src="/assets/rizwan.jpg" alt="Rizwan Hassan" />
                <div className="image-overlay"></div>
              </div>
              <div className="member-info">
                <h3>Rizwan Hassan</h3>
                <span className="member-position">Chief Executive Officer</span>
                <p>Visionary leader driving technological innovation and business strategy.</p>
                <div className="member-social">
                  <a
                    href="https://www.linkedin.com/in/rizwan-gul-hassan-6bb545272/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="social-link"
                  >
                    <i className="fab fa-linkedin"></i>
                  </a>
                </div>
              </div>
            </div>
            <div className="team-member-card">
              <div className="member-image">
                <img src="/assets/moaaz.jpg" alt="Moaaz Khokhar" />
                <div className="image-overlay"></div>
              </div>
              <div className="member-info">
                <h3>Moaaz Khokhar</h3>
                <span className="member-position">Chief Technology Officer</span>
                <p>Architect of efficient software and AI systems with extensive technical expertise.</p>
                <div className="member-social">
                  <a
                    href="https://www.linkedin.com/in/moaaz-khokhar/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="social-link"
                  >
                    <i className="fab fa-linkedin"></i>
                  </a>
                </div>
              </div>
            </div>
            <div className="team-member-card">
              <div className="member-image">
                <img src="/assets/imran.jpg" alt="Imran Hassan" />
                <div className="image-overlay"></div>
              </div>
              <div className="member-info">
                <h3>Imran Hassan</h3>
                <span className="member-position">Head of AI Projects</span>
                <p>Leading our data management initiatives and AI project implementation.</p>
                <div className="member-social">
                  <a
                    href="https://www.linkedin.com/in/imran-hassan-101679266/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="social-link"
                  >
                    <i className="fab fa-linkedin"></i>
                  </a>
                </div>
              </div>
            </div>
            <div className="team-member-card">
              <div className="member-image">
                <img src="/assets/mueez.png" alt="Mueez ur Rehman" />
                <div className="image-overlay"></div>
              </div>
              <div className="member-info">
                <h3>Mueez ur Rehman</h3>
                <span className="member-position">Lead GenerativeAI</span>
                <p>Building cutting-edge solutions at the forefront of generative technology.</p>
                <div className="member-social">
                  <a
                    href="https://www.linkedin.com/in/mueez-ur-rehman-759110202/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="social-link"
                  >
                    <i className="fab fa-linkedin"></i>
                  </a>
                </div>
              </div>
            </div>
            <div className="team-member-card">
              <div className="member-image">
                <img src="/assets/talha.jpg" alt="Talha Tariq" />
                <div className="image-overlay"></div>
              </div>
              <div className="member-info">
                <h3>Talha Tariq</h3>
                <span className="member-position">Lead ML Optimization</span>
                <p>Specialist in machine learning and problem-oriented optimization solutions.</p>
                <div className="member-social">
                  <a
                    href="https://www.linkedin.com/in/talha-tariq-0ab74a46/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="social-link"
                  >
                    <i className="fab fa-linkedin"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="values" className="section">
        <div className="container">
          <div className="section-header">
            <span className="section-subtitle">What Drives Us</span>
            <h2 className="section-title">Our Core Values</h2>
            <p className="section-description">
              These principles guide everything we do and define our approach to business.
            </p>
          </div>
          <div className="values-grid">
            <div className="value-card">
              <div className="value-icon">
                <i className="fas fa-lightbulb"></i>
              </div>
              <h3>Innovation</h3>
              <p>We constantly push boundaries and explore new possibilities to create cutting-edge solutions.</p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <i className="fas fa-users"></i>
              </div>
              <h3>Collaboration</h3>
              <p>We believe in the power of teamwork and partnership to achieve extraordinary results.</p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <i className="fas fa-chart-line"></i>
              </div>
              <h3>Excellence</h3>
              <p>We strive for the highest standards in everything we do, from code quality to client service.</p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <i className="fas fa-shield-alt"></i>
              </div>
              <h3>Integrity</h3>
              <p>We operate with honesty, transparency, and ethical practices in all our relationships.</p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <i className="fas fa-rocket"></i>
              </div>
              <h3>Adaptability</h3>
              <p>We embrace change and continuously evolve to meet emerging technological challenges.</p>
            </div>
            <div className="value-card">
              <div className="value-icon">
                <i className="fas fa-handshake"></i>
              </div>
              <h3>Client Focus</h3>
              <p>We prioritize understanding our clients' needs and delivering solutions that exceed expectations.</p>
            </div>
          </div>
        </div>
      </section>

      <section id="cta" className="section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Transform Your Business?</h2>
            <p>Connect with us to discuss how Qoverse can help you achieve your technology goals.</p>
            <Link href="/contact" className="cta-button primary">
              <span className="button-text">Contact Us Today</span>
              <span className="button-icon">→</span>
            </Link>
          </div>
        </div>
      </section>
    </>
  )
}
