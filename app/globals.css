/* Modern AI/ML Font Imports */
@import url("https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700;800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap");
@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap");
@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css");

/* CSS Variables for Theming */
:root {
  /* Modern AI/ML Color Palette - Dark Theme (Default) */
  --primary-bg: #0a0b1e;
  --secondary-bg: #0f1129;
  --tertiary-bg: #151a35;
  --quaternary-bg: #1a2040;

  /* AI-Focused Accent Colors */
  --accent-color: #00d4ff;
  --accent-secondary: #6366f1;
  --accent-tertiary: #8b5cf6;
  --accent-hover: #33ddff;
  --accent-glow: rgba(0, 212, 255, 0.3);

  /* Neural Network Inspired Colors */
  --neural-blue: #0ea5e9;
  --neural-purple: #a855f7;
  --neural-cyan: #06b6d4;
  --neural-pink: #ec4899;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-accent: #00d4ff;

  /* Border and Surface Colors */
  --border-color: rgba(0, 212, 255, 0.2);
  --border-secondary: rgba(99, 102, 241, 0.2);
  --surface-glass: rgba(15, 17, 41, 0.8);
  --surface-card: rgba(21, 26, 53, 0.9);

  /* Status Colors */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;

  /* Modern Gradients */
  --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #6366f1 50%, #8b5cf6 100%);
  --gradient-secondary: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 100%);
  --gradient-neural: linear-gradient(135deg, #a855f7 0%, #ec4899 50%, #00d4ff 100%);
  --gradient-dark: linear-gradient(135deg, #0a0b1e 0%, #151a35 100%);
  --gradient-glass: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);

  /* Enhanced Shadows */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.5);
  --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);
  --shadow-neural: 0 0 30px rgba(168, 85, 247, 0.2);

  /* Layout */
  --header-height: 70px;
  --border-radius: 12px;
  --border-radius-lg: 16px;

  /* Animations */
  --animation-slow: 0.6s;
  --animation-medium: 0.4s;
  --animation-fast: 0.2s;
  --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Font Settings */
  --font-heading: var(--font-space-grotesk);
  --font-body: var(--font-inter);
  --font-mono: 'JetBrains Mono', monospace;
}

/* Light Theme Variables */
[data-theme="light"] {
  --primary-bg: #ffffff;
  --secondary-bg: #f8fafc;
  --tertiary-bg: #f1f5f9;
  --quaternary-bg: #e2e8f0;

  /* Light theme accent adjustments */
  --accent-color: #0ea5e9;
  --accent-secondary: #6366f1;
  --accent-tertiary: #8b5cf6;
  --accent-hover: #0284c7;
  --accent-glow: rgba(14, 165, 233, 0.2);

  --text-primary: #0f172a;
  --text-secondary: #334155;
  --text-muted: #64748b;
  --text-accent: #0ea5e9;

  --border-color: rgba(14, 165, 233, 0.2);
  --border-secondary: rgba(99, 102, 241, 0.2);
  --surface-glass: rgba(248, 250, 252, 0.8);
  --surface-card: rgba(241, 245, 249, 0.9);

  --gradient-dark: linear-gradient(135deg, #f8fafc, #e2e8f0);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* Base Reset */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 62.5%; /* 10px base for easier rems */
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: var(--accent-color) var(--tertiary-bg);
}

body {
  font-family: var(--font-body);
  font-size: 1.6rem;
  line-height: 1.7;
  color: var(--text-primary);
  background-color: var(--primary-bg);
  overflow-x: hidden;
  transition: background-color 0.5s ease, color 0.5s ease;
  padding-top: var(--header-height);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--tertiary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-hover);
}

/* Modern Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 2rem;
  color: var(--text-primary);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

/* Fallback for browsers that don't support background-clip */
@supports not (-webkit-background-clip: text) {
  h1, h2, h3, h4, h5, h6 {
    color: var(--accent-color);
    background: none;
    -webkit-text-fill-color: initial;
  }
}

h1 {
  font-size: 5.5rem;
  letter-spacing: -0.03em;
  font-weight: 800;
  text-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
}

h2 {
  font-size: 4.8rem;
  letter-spacing: -0.02em;
  font-weight: 700;
}

h3 {
  font-size: 3.2rem;
  letter-spacing: -0.01em;
}

h4 {
  font-size: 2.4rem;
}

p {
  margin-bottom: 1.8rem;
  color: var(--text-secondary);
  line-height: 1.7;
  font-weight: 400;
}

/* Enhanced paragraph styles for better readability */
.lead-text {
  font-size: 2rem;
  font-weight: 500;
  line-height: 1.6;
  color: var(--text-primary);
  margin-bottom: 2.5rem;
}

.body-large {
  font-size: 1.8rem;
  line-height: 1.6;
}

.body-small {
  font-size: 1.4rem;
  line-height: 1.5;
}

/* Code and technical text */
.code-text, code {
  font-family: var(--font-mono);
  background: var(--surface-card);
  padding: 0.2rem 0.6rem;
  border-radius: 4px;
  font-size: 0.9em;
  color: var(--accent-color);
  border: 1px solid var(--border-color);
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: all var(--animation-medium) ease;
  position: relative;
}

a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: width var(--animation-medium) ease;
}

a:hover {
  color: var(--accent-hover);
  transform: translateY(-1px);
}

a:hover::after {
  width: 100%;
}

ul,
ol {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
}

/* Modern Background Patterns */
.neural-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(168, 85, 247, 0.05) 0%, transparent 50%);
  z-index: -1;
  animation: neural-pulse 8s ease-in-out infinite;
}

.circuit-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px),
    linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px);
  background-size: 60px 60px;
  z-index: -1;
  opacity: 0.3;
}

.ai-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: -1;
}

@keyframes neural-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

/* Interactive Elements and Animations */
.glow-on-hover {
  position: relative;
  transition: all 0.3s ease;
}

.glow-on-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  background: var(--gradient-primary);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.glow-on-hover:hover::before {
  opacity: 0.1;
}

.glow-on-hover:hover {
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

/* Floating Animation */
.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Pulse Animation */
.pulse-animation {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
    transform: scale(1.02);
  }
}

/* Shimmer Effect */
.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.8s ease;
}

.shimmer-effect:hover::before {
  left: 100%;
}

/* Magnetic Effect */
.magnetic-effect {
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.magnetic-effect:hover {
  transform: scale(1.05) rotate(2deg);
}

/* Neural Network Lines Animation */
.neural-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(45deg, transparent 40%, rgba(0, 212, 255, 0.1) 50%, transparent 60%),
    linear-gradient(-45deg, transparent 40%, rgba(99, 102, 241, 0.1) 50%, transparent 60%);
  background-size: 200px 200px;
  animation: neural-flow 10s linear infinite;
  z-index: -1;
}

@keyframes neural-flow {
  0% { background-position: 0 0, 0 0; }
  100% { background-position: 200px 200px, -200px 200px; }
}

/* Advanced AI-themed Animations */
.matrix-rain {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(0deg, transparent 24%, rgba(0, 212, 255, 0.05) 25%, rgba(0, 212, 255, 0.05) 26%, transparent 27%, transparent 74%, rgba(0, 212, 255, 0.05) 75%, rgba(0, 212, 255, 0.05) 76%, transparent 77%, transparent);
  background-size: 50px 50px;
  animation: matrix-fall 20s linear infinite;
  z-index: -1;
}

@keyframes matrix-fall {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100vh); }
}

/* Holographic Effect */
.holographic {
  position: relative;
  background: linear-gradient(45deg, transparent 30%, rgba(0, 212, 255, 0.1) 50%, transparent 70%);
  background-size: 200% 200%;
  animation: holographic-shift 3s ease-in-out infinite;
}

@keyframes holographic-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Data Stream Animation */
.data-stream-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 98px,
      rgba(0, 212, 255, 0.03) 100px
    );
  animation: data-flow 15s linear infinite;
  z-index: -1;
}

@keyframes data-flow {
  0% { transform: translateX(-100px); }
  100% { transform: translateX(100px); }
}

/* Quantum Particles */
.quantum-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(1px 1px at 20px 30px, rgba(0, 212, 255, 0.4), transparent),
    radial-gradient(1px 1px at 40px 70px, rgba(99, 102, 241, 0.4), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(168, 85, 247, 0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(236, 72, 153, 0.4), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  animation: quantum-drift 25s linear infinite;
  z-index: -1;
}

@keyframes quantum-drift {
  0% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
  100% { transform: translate(0, 0) rotate(360deg); }
}

/* AI Processing Indicator */
.ai-processing {
  position: relative;
}

.ai-processing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.2), transparent);
  animation: ai-scan 2s ease-in-out infinite;
  z-index: 1;
}

@keyframes ai-scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Neural Synapse Effect */
.neural-synapse {
  position: relative;
  overflow: hidden;
}

.neural-synapse::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 2px;
  background: var(--accent-color);
  border-radius: 50%;
  box-shadow:
    0 0 10px var(--accent-color),
    0 0 20px var(--accent-color),
    0 0 30px var(--accent-color);
  animation: synapse-fire 3s ease-in-out infinite;
  z-index: 1;
}

@keyframes synapse-fire {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -50%) scale(20);
    opacity: 1;
  }
}

/* Cyberpunk Glow */
.cyberpunk-glow {
  position: relative;
  background: var(--surface-card);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.cyberpunk-glow:hover {
  border-color: var(--accent-color);
  box-shadow:
    0 0 20px rgba(0, 212, 255, 0.3),
    inset 0 0 20px rgba(0, 212, 255, 0.1);
  background: rgba(0, 212, 255, 0.05);
}

.cyberpunk-glow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--accent-color), var(--accent-secondary), var(--accent-tertiary), var(--accent-color));
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cyberpunk-glow:hover::before {
  opacity: 1;
  animation: cyberpunk-border 2s linear infinite;
}

@keyframes cyberpunk-border {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Preloader */
#preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-bg);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loader {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loader svg {
  width: 8rem;
  height: 8rem;
}

#loader-circle {
  fill: none;
  stroke: var(--accent-color);
  stroke-width: 2;
  stroke-dasharray: 200;
  stroke-dashoffset: 200;
  animation: dash 2s ease-in-out infinite;
}

.loader-text {
  margin-top: 2rem;
  font-family: var(--font-heading);
  font-size: 1.8rem;
  letter-spacing: 0.2em;
  color: var(--accent-color);
  font-weight: 700;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes dash {
  0% {
    stroke-dashoffset: 200;
  }
  50% {
    stroke-dashoffset: 50;
  }
  100% {
    stroke-dashoffset: 200;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* ========== DROPDOWN MENU FIX ========== */

/* Override the hover-based dropdown display with JavaScript-controlled display */
.dropdown .dropdown-wrapper {
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%) translateY(1rem);
  transition: all 0.3s ease;
}

/* Only use these styles when JavaScript sets the "active" class */
.dropdown.active .dropdown-wrapper {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

/* Disable CSS hover behavior which is causing issues */
.dropdown:hover .dropdown-wrapper {
  opacity: 0;
  visibility: hidden;
}

/* Only when JavaScript adds active class, use the hover styles */
.dropdown.active:hover .dropdown-wrapper {
  opacity: 1;
  visibility: visible;
}

/* Mobile-specific dropdown styling */
@media (max-width: 767px) {
  .dropdown-wrapper {
    position: static;
    min-width: auto;
    transform: none;
    width: 100%;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease, opacity 0.3s ease, visibility 0.3s ease;
    padding: 0;
    margin: 0;
  }
  
  .dropdown.active .dropdown-wrapper {
    max-height: 1000px;
    margin: 1rem 0;
    transform: none;
    background: transparent;
    box-shadow: none;
    border: none;
  }
  
  .dropdown-content {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding-left: 2rem;
  }
  
  .dropdown-link {
    padding: 0.8rem 1rem;
    background: rgba(65, 105, 225, 0.05);
  }
}

/* ========== HERO SECTION LAYOUT FIX ========== */

/* Reset the hero section layout */
#hero {
  position: relative;
  overflow: visible;
  min-height: 80vh;
  display: flex;
  align-items: center;
  background: var(--gradient-dark);
  padding: calc(16rem + var(--header-height)) 0 10rem; /* Increased bottom padding from 8rem to 10rem */
}

/* Fix container alignment */
#hero .container {
  position: relative;
  z-index: 2;
  width: 100%;
}

/* Structure the hero content */
.hero-content {
  display: flex;
  flex-direction: column;
  gap: 3rem; /* Reduced from 5rem to 3rem to minimize vertical space */
  position: relative;
  z-index: 1001;
  padding-top: 2rem;
}

/* Text area styling */
.hero-text {
  max-width: 60rem;
  margin: 9rem auto 0; /* Increased top margin from 7rem to 9rem */
  text-align: center;
}

/* Fix the highlights section - critical fix */
.hero-highlights {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 2rem;
  width: 100%;
  /* Fallback for older browsers */
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

/* Ensure each card is visible with proper styling */
.highlight-card {
  background: rgba(10, 10, 20, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(65, 105, 225, 0.2);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Medium screens */
@media (max-width: 992px) {
  .hero-highlights {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  .highlight-card {
    padding: 1.5rem;
  }
}

/* Small screens */
@media (max-width: 768px) {
  .hero-highlights {
    grid-template-columns: 1fr;
    max-width: 500px;
    margin: 2rem auto 0;
  }
  
  .hero-text {
    text-align: center;
  }
  
  .hero-cta {
    display: flex;
    justify-content: center;
  }
}

/* Extra small screens */
@media (max-width: 576px) {
  #hero {
    padding: 7rem 0 4rem;
  }
  
  .hero-text h1 {
    font-size: 3.6rem;
  }
  
  .highlight-card {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }
  
  .highlight-icon {
    width: 4rem;
    height: 4rem;
    font-size: 1.8rem;
    flex-shrink: 0;
  }
}


/* Custom Cursor */
.cursor-dot,
.cursor-outline {
  pointer-events: none;
  position: fixed;
  top: 0;
  left: 0;
  border-radius: 50%;
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.cursor-dot {
  width: 8px;
  height: 8px;
  background-color: var(--accent-color);
  transform: translate(-50%, -50%);
  transition: transform 0.1s ease;
}

.cursor-outline {
  width: 40px;
  height: 40px;
  border: 2px solid rgba(65, 105, 225, 0.5);
  transform: translate(-50%, -50%);
  transition: all 0.2s ease;
}

/* Header */
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--header-height);
  background: rgba(10, 10, 20, 0.8);
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: background 0.3s ease, transform 0.3s ease;
}

[data-theme="light"] header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

header.hidden {
  transform: translateY(-100%);
}

header.solid {
  background: var(--primary-bg);
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-img {
  height: 4rem;
  transition: transform 0.3s ease;
}

.logo-img:hover {
  transform: scale(1.1);
}

/* Navigation */
nav {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-toggle {
  display: none;
  cursor: pointer;
  width: 3rem;
  height: 3rem;
  position: relative;
  z-index: 1010;
}

.hamburger {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  cursor: pointer;
}

.hamburger span {
  display: block;
  width: 100%;
  height: 2px;
  background: var(--text-primary);
  transition: all 0.3s ease;
}

.nav-toggle.active .hamburger span:nth-child(1) {
  transform: translateY(8px) rotate(45deg);
}

.nav-toggle.active .hamburger span:nth-child(2) {
  opacity: 0;
}

.nav-toggle.active .hamburger span:nth-child(3) {
  transform: translateY(-8px) rotate(-45deg);
}

.nav-links {
  display: flex;
  gap: 3rem;
  align-items: center;
}

.nav-link {
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  padding: 0.5rem 1rem;
  position: relative;
  transition: color 0.3s ease;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--accent-color);
  transition: width 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Dropdown Menu */
.dropdown {
  position: relative;
}

.dropdown-wrapper {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  min-width: 28rem;
  background: rgba(26, 26, 42, 0.95);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  opacity: 0;
  visibility: hidden;
  transform-origin: top center;
  transform: translateX(-50%) translateY(1rem);
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: 100;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(65, 105, 225, 0.3);
  overflow: hidden;
}

/* .dropdown:hover .dropdown-wrapper {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
} */

.dropdown-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.2rem;
  max-height: 65vh;
  overflow-y: auto;
  padding-right: 0.6rem;
}

.dropdown-content::-webkit-scrollbar {
  width: 6px;
}

.dropdown-content::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #4169e1, #8a2be2);
  border-radius: 6px;
  transition: background 0.3s ease;
}

.dropdown-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #8a2be2, #ff69b4);
}

.dropdown-link {
  font-size: 1.5rem;
  padding: 1.4rem 1.8rem;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  border-left: 4px solid transparent;
  background: linear-gradient(135deg, rgba(65, 105, 225, 0.1), rgba(138, 43, 226, 0.1));
  color: #e6e6fa;
  text-shadow: 0 0 5px rgba(138, 43, 226, 0.3);
}

.dropdown-link span {
  display: inline-block;
  margin-right: 1.5rem;
  color: #ff69b4;
  font-weight: 700;
  font-family: "Montserrat", sans-serif;
  opacity: 1;
  min-width: 3rem;
  text-align: center;
  background: rgba(255, 105, 180, 0.2);
  border-radius: 50%;
  padding: 0.5rem;
}

.dropdown-link:hover {
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.2), rgba(255, 105, 180, 0.2));
  color: #fff;
  border-left-color: #ff69b4;
  transform: translateX(8px) scale(1.05);
  box-shadow: 0 5px 15px rgba(255, 105, 180, 0.3);
}

/* Mobile Navigation Improvements */
.nav-link-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
}

.dropdown-toggle {
  display: none;
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.dropdown.active .dropdown-toggle i {
  transform: rotate(180deg);
}

/* Theme Toggle */
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  cursor: pointer;
  position: relative;
  margin-left: 2rem;
}

.theme-icon {
  position: absolute;
  transition: opacity 0.3s ease, transform 0.5s ease;
  font-size: 1.8rem;
}

.theme-icon.light-icon {
  opacity: 0;
  transform: rotate(90deg);
}

.theme-icon.dark-icon {
  opacity: 1;
  transform: rotate(0);
}

[data-theme="light"] .theme-icon.light-icon {
  opacity: 1;
  transform: rotate(0);
}

[data-theme="light"] .theme-icon.dark-icon {
  opacity: 0;
  transform: rotate(-90deg);
}

/* Hero Section */
#hero {
  position: relative;
  height: 90vh; /* Reduced from 100vh to better fit smaller viewports */
  min-height: 50rem; /* Reduced from 60rem */
  display: flex;
  align-items: center;
  overflow: visible;
  padding-top: var(--header-height);
}

#three-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.parallax {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("/assets/Loop_wave.gif") no-repeat center center / cover;
  opacity: 0.2;
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 5rem;
}

.hero-text {
  max-width: 60rem;
}

.hero-text h1 {
  position: relative;
  margin-bottom: 1.5rem;
}

/* Glitch Effect */
.glitch-effect {
  position: static; /* Ensure the h1 itself isn’t absolutely positioned */
  display: inline-block; /* Ensure it behaves as a block for proper centering */
  color: var(--text-primary);
}

.glitch-effect::before,
.glitch-effect::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  clip: rect(0, 0, 0, 0);
}

.glitch-effect::before {
  left: -2px;
  text-shadow: 2px 0 var(--accent-color);
  animation: glitch-1 2s infinite linear alternate-reverse;
}

.glitch-effect::after {
  left: 2px;
  text-shadow: -2px 0 var(--accent-hover);
  animation: glitch-2 3s infinite linear alternate-reverse;
}

@keyframes glitch-1 {
  0% {
    clip: rect(36px, 9999px, 28px, 0);
  }
  5% {
    clip: rect(85px, 9999px, 95px, 0);
  }
  10% {
    clip: rect(12px, 9999px, 48px, 0);
  }
  15% {
    clip: rect(64px, 9999px, 78px, 0);
  }
  20% {
    clip: rect(30px, 9999px, 16px, 0);
  }
  25% {
    clip: rect(71px, 9999px, 88px, 0);
  }
  30% {
    clip: rect(10px, 9999px, 85px, 0);
  }
  35% {
    clip: rect(80px, 9999px, 10px, 0);
  }
  40% {
    clip: rect(92px, 9999px, 59px, 0);
  }
  45% {
    clip: rect(23px, 9999px, 70px, 0);
  }
  50% {
    clip: rect(38px, 9999px, 92px, 0);
  }
  55% {
    clip: rect(3px, 9999px, 24px, 0);
  }
  60% {
    clip: rect(26px, 9999px, 75px, 0);
  }
  65% {
    clip: rect(73px, 9999px, 52px, 0);
  }
  70% {
    clip: rect(84px, 9999px, 33px, 0);
  }
  75% {
    clip: rect(66px, 9999px, 24px, 0);
  }
  80% {
    clip: rect(40px, 9999px, 70px, 0);
  }
  85% {
    clip: rect(29px, 9999px, 90px, 0);
  }
  90% {
    clip: rect(18px, 9999px, 40px, 0);
  }
  95% {
    clip: rect(77px, 9999px, 62px, 0);
  }
  100% {
    clip: rect(14px, 9999px, 86px, 0);
  }
}

@keyframes glitch-2 {
  0% {
    clip: rect(19px, 9999px, 42px, 0);
  }
  5% {
    clip: rect(68px, 9999px, 10px, 0);
  }
  10% {
    clip: rect(9px, 9999px, 87px, 0);
  }
  15% {
    clip: rect(18px, 9999px, 69px, 0);
  }
  20% {
    clip: rect(87px, 9999px, 22px, 0);
  }
  25% {
    clip: rect(5px, 9999px, 51px, 0);
  }
  30% {
    clip: rect(36px, 9999px, 40px, 0);
  }
  35% {
    clip: rect(2px, 9999px, 46px, 0);
  }
  40% {
    clip: rect(51px, 9999px, 72px, 0);
  }
  45% {
    clip: rect(7px, 9999px, 30px, 0);
  }
  50% {
    clip: rect(65px, 9999px, 99px, 0);
  }
  55% {
    clip: rect(75px, 9999px, 56px, 0);
  }
  60% {
    clip: rect(82px, 9999px, 31px, 0);
  }
  65% {
    clip: rect(54px, 9999px, 81px, 0);
  }
  70% {
    clip: rect(20px, 9999px, 65px, 0);
  }
  75% {
    clip: rect(15px, 9999px, 84px, 0);
  }
  80% {
    clip: rect(92px, 9999px, 3px, 0);
  }
  85% {
    clip: rect(83px, 9999px, 24px, 0);
  }
  90% {
    clip: rect(57px, 9999px, 78px, 0);
  }
  95% {
    clip: rect(70px, 9999px, 41px, 0);
  }
  100% {
    clip: rect(35px, 9999px, 73px, 0);
  }
}
/* Improved Typewriter Effect */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--accent-color);
  white-space: nowrap;
  margin: 0 auto;
  letter-spacing: 0.05em;
  width: 100%; /* Ensure the container is full width */
  animation: 
    typing 3.5s steps(60, end), /* Increased steps for smoother animation */
    blink-caret 0.75s step-end infinite;
  animation-delay: 0.5s; /* Reduced delay */
  animation-fill-mode: both;
  font-size: 2.2rem;
  max-width: 60rem;
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: var(--accent-color) }
}

/* Hero CTA */
.hero-cta {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  margin-top: 5rem; /* Increased from 3rem to 5rem to move the button down */
}

/* Modern CTA Buttons */
.cta-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1.2rem 2.5rem;
  border-radius: var(--border-radius-lg);
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all var(--animation-medium) var(--animation-bounce);
  cursor: pointer;
  border: none;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  text-decoration: none;
}

.cta-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2));
  transform: translateX(-100%) skewX(-15deg);
  transition: transform 0.6s ease;
}

.cta-button:hover::before {
  transform: translateX(100%) skewX(-15deg);
}

.cta-button.primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-glow);
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.cta-button.primary:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4), var(--shadow-neural);
}

.cta-button.secondary {
  background: var(--surface-glass);
  color: var(--text-primary);
  border: 2px solid var(--accent-color);
  backdrop-filter: blur(15px);
}

.cta-button.secondary:hover {
  background: rgba(0, 212, 255, 0.1);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-glow);
}

.cta-button.tertiary {
  background: var(--surface-card);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.cta-button.tertiary:hover {
  background: var(--tertiary-bg);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-md);
}

.button-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.cta-button:hover .button-icon {
  transform: translateX(5px);
}

.play-icon {
  font-size: 1.4rem;
}

/* Hero Highlights
.hero-highlights {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
} */

/* Hero Highlights */
.highlight-card {
  flex: 1;
  min-width: 20rem;
  background: rgba(10, 10, 20, 0.6);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.highlight-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.highlight-icon {
  width: 5rem;
  height: 5rem;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.2rem;
  color: white;
}

.highlight-text h3 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.highlight-text p {
  font-size: 1.4rem;
  margin-bottom: 0;
}

/* Scroll Indicator */
/* .scroll-indicator {
  position: absolute;
  bottom: 4rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  z-index: 2;
  animation: bounce 2s infinite;
}

.mouse {
  width: 3rem;
  height: 5rem;
  border: 2px solid var(--text-primary);
  border-radius: 2rem;
  position: relative;
}

.mouse-wheel {
  width: 0.6rem;
  height: 0.6rem;
  background: var(--text-primary);
  border-radius: 50%;
  position: absolute;
  top: 30%;
  left: 50%;
  transform: translateX(-50%);
  animation: scroll 1.5s infinite;
}

.scroll-text {
  font-size: 1.2rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: var(--text-secondary);
} */

@keyframes scroll {
  0% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(1.5rem);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}


/* Home Page Specific Styles */

/* Video Modal Styling */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1100;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(10, 10, 20, 0.9);
  backdrop-filter: blur(10px);
}

.modal-container {
  position: relative;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  background: var(--secondary-bg);
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  z-index: 1;
  transform: scale(0.9);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal.active .modal-container {
  transform: scale(1);
}

.modal-close {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  cursor: pointer;
  z-index: 10;
  transition: background 0.3s ease, transform 0.3s ease;
}

.modal-close:hover {
  background: var(--accent-color);
  transform: rotate(90deg);
}

.modal-content {
  width: 100%;
  height: 100%;
}

.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
}

.video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Hero Section Improvements */
.hero-text {
  margin-bottom: 6rem;
}

.hero-cta {
  margin-top: 4rem;
}

.play-showreel {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.play-showreel::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  transform: skewX(-30deg);
  transition: all 0.5s ease;
}

.play-showreel:hover::before {
  left: 100%;
}

.play-icon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2.8rem;
  height: 2.8rem;
  background: var(--accent-color);
  border-radius: 50%;
  color: white;
  transition: all 0.3s ease;
}

.play-showreel:hover .play-icon {
  transform: scale(1.1);
}

/* Hero Highlights Improvements */
/* .hero-highlights {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 3rem;
} */

.highlight-card {
  background: rgba(10, 10, 20, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(65, 105, 225, 0.2);
  transition: all 0.3s ease;
}

.highlight-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 30px rgba(65, 105, 225, 0.3);
  border-color: rgba(65, 105, 225, 0.5);
}

/* Services Preview Improvements */
.services-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
  gap: 3rem;
  margin-bottom: 5rem;
}

.service-card {
  background: var(--secondary-bg);
  border-radius: 1.5rem;
  padding: 3rem;
  box-shadow: var(--shadow-md);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  z-index: 1;
  border: 1px solid transparent;
  overflow: hidden;
}

.service-card:hover {
  transform: translateY(-15px);
  box-shadow: var(--shadow-lg);
  border-color: rgba(65, 105, 225, 0.3);
}

.service-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.5s ease;
}

.service-card:hover .service-background {
  opacity: 0.05;
}

.service-icon {
  width: 7rem;
  height: 7rem;
  margin-bottom: 2.5rem;
  background: rgba(65, 105, 225, 0.1);
  border-radius: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: var(--accent-color);
  transition: all 0.3s ease;
}

.service-card:hover .service-icon {
  background: var(--accent-color);
  color: white;
  transform: scale(1.1);
}

.service-features {
  margin: 2rem 0;
}

.service-link {
  display: inline-flex;
  align-items: center;
  font-weight: 600;
  color: var(--accent-color);
  transition: all 0.3s ease;
}

.service-link::after {
  content: '→';
  margin-left: 0.8rem;
  transition: transform 0.3s ease;
}

.service-link:hover::after {
  transform: translateX(5px);
}

/* About Preview Improvements */
.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: center;
}

.about-features {
  margin: 4rem 0;
}

.feature-item {
  display: flex;
  gap: 2rem;
  margin-bottom: 2.5rem;
}

.feature-icon {
  flex-shrink: 0;
  width: 4.5rem;
  height: 4.5rem;
  background: rgba(65, 105, 225, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--accent-color);
  transition: all 0.3s ease;
}

.feature-item:hover .feature-icon {
  background: var(--accent-color);
  color: white;
  transform: scale(1.1);
}

.about-image {
  position: relative;
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.about-image:hover {
  transform: scale(1.02);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: -5rem;
  position: relative;
  z-index: 2;
}

.stat-item {
  background: var(--secondary-bg);
  border-radius: 1rem;
  padding: 2.5rem 1.5rem;
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  border: 1px solid rgba(65, 105, 225, 0.1);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: rgba(65, 105, 225, 0.3);
}

.stat-number {
  font-family: var(--font-heading);
  font-size: 4rem;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
  display: inline-block;
}

.stat-symbol {
  font-family: var(--font-heading);
  font-size: 2.8rem;
  font-weight: 700;
  color: var(--accent-color);
  display: inline-block;
  margin-left: 0.2rem;
}

.stat-label {
  font-size: 1.4rem;
  color: var(--text-secondary);
  margin-top: 1rem;
  display: block;
  font-weight: 500;
}

/* Process Timeline Improvements */
.process-timeline {
  max-width: 80rem;
  margin: 0 auto;
  position: relative;
  padding-left: 3rem;
}

.process-timeline::before {
  content: "";
  position: absolute;
  top: 0;
  left: 5rem;
  width: 2px;
  height: 100%;
  background: var(--accent-color);
  opacity: 0.3;
}

.timeline-node {
  position: relative;
  padding-left: 8rem;
  margin-bottom: 5rem;
}

.timeline-node:last-child {
  margin-bottom: 0;
}

.node-number {
  position: absolute;
  left: 0;
  top: 0;
  width: 10rem;
  height: 5rem;
  background: var(--secondary-bg);
  border-radius: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-heading);
  font-size: 2.4rem;
  font-weight: 700;
  color: var(--accent-color);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  z-index: 2;
}

.timeline-node:hover .node-number {
  background: var(--accent-color);
  color: white;
  transform: scale(1.05);
}

.node-content {
  background: var(--secondary-bg);
  border-radius: 1.5rem;
  padding: 3rem;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
  border: 1px solid transparent;
}

.timeline-node:hover .node-content {
  transform: translateX(10px);
  box-shadow: var(--shadow-md);
  border-color: rgba(65, 105, 225, 0.2);
}

/* Testimonials Improvements */
.testimonials-slider {
  position: relative;
  max-width: 90rem;
  margin: 0 auto;
}

.testimonial-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  opacity: 0;
  visibility: hidden;
  transform: translateX(50px);
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.testimonial-slide.active {
  position: relative;
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.testimonial-card {
  background: var(--secondary-bg);
  border-radius: 1.5rem;
  padding: 4rem;
  box-shadow: var(--shadow-md);
  position: relative;
  border: 1px solid rgba(65, 105, 225, 0.1);
  transition: all 0.3s ease;
}

.testimonial-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: rgba(65, 105, 225, 0.2);
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: 2rem;
  left: 2rem;
  font-family: var(--font-heading);
  font-size: 12rem;
  line-height: 1;
  color: var(--accent-color);
  opacity: 0.1;
}

.testimonial-quote {
  font-size: 2rem;
  font-style: italic;
  color: var(--text-primary);
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
  line-height: 1.7;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.author-image {
  width: 8rem;
  height: 8rem;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border: 3px solid var(--accent-color);
  transition: all 0.3s ease;
}

.author-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.testimonial-card:hover .author-image {
  transform: scale(1.05);
}

.author-info h4 {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.author-info p {
  font-size: 1.5rem;
  color: var(--accent-color);
  font-weight: 500;
}

.testimonial-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3rem;
  margin-top: 4rem;
}

.control-prev,
.control-next {
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  background: var(--tertiary-bg);
  border: none;
  font-size: 2rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-prev:hover,
.control-next:hover {
  background: var(--accent-color);
  color: white;
  transform: scale(1.1);
}

.control-indicators {
  display: flex;
  gap: 1.2rem;
}

.indicator {
  width: 1.2rem;
  height: 1.2rem;
  border-radius: 50%;
  background: var(--tertiary-bg);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: var(--accent-color);
  transform: scale(1.2);
}

.indicator:hover {
  background: var(--accent-color);
  opacity: 0.7;
}

/* Contact Preview Improvements */
.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5rem;
  align-items: start;
}

.contact-info {
  padding-right: 2rem;
}

.contact-methods {
  margin: 4rem 0;
  display: grid;
  gap: 2.5rem;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
}

.method-icon {
  flex-shrink: 0;
  width: 5rem;
  height: 5rem;
  background: rgba(65, 105, 225, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--accent-color);
  transition: all 0.3s ease;
}

.contact-method:hover .method-icon {
  background: var(--accent-color);
  color: white;
  transform: scale(1.1);
}

.method-details h4 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.contact-preview-cta {
  margin-top: 3rem;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .hero-highlights {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .about-content,
  .contact-content {
    gap: 4rem;
  }
}

@media (max-width: 992px) {
  .about-content,
  .contact-content {
    grid-template-columns: 1fr;
  }
  
  .about-image-container {
    order: -1;
  }
  
  .about-stats {
    margin-top: -4rem;
  }
  
  .hero-highlights {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .services-showcase {
    grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  }
  
  .process-timeline {
    padding-left: 0;
  }
  
  .process-timeline::before {
    left: 2rem;
  }
  
  .timeline-node {
    padding-left: 4rem;
  }
  
  .node-number {
    width: 4rem;
    height: 4rem;
    font-size: 1.6rem;
  }
}

@media (max-width: 768px) {
  .hero-highlights {
    grid-template-columns: 1fr;
  }
  
  .modal-container {
    width: 95%;
  }
  
  .about-stats {
    grid-template-columns: repeat(3, 1fr);
    margin-top: 3rem;
  }
  
  .stat-item {
    padding: 2rem 1rem;
  }
  
  .stat-number {
    font-size: 3rem;
  }
  
  .testimonial-card {
    padding: 3rem;
  }
  
  .testimonial-quote {
    font-size: 1.8rem;
  }
  
  .contact-form-container {
    margin-top: 4rem;
  }
}

@media (max-width: 576px) {
  .about-stats {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .feature-item {
    gap: 1.5rem;
  }
  
  .feature-icon {
    width: 4rem;
    height: 4rem;
  }
  
  .service-card {
    padding: 2.5rem;
  }
  
  .testimonial-controls {
    gap: 1.5rem;
  }
  
  .control-prev,
  .control-next {
    width: 4rem;
    height: 4rem;
  }
  
  .testimonial-author {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.5rem;
  }
}
/* Modern Section Styles */
.section {
  padding: 12rem 0;
  position: relative;
  overflow: hidden;
}

.section-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.08) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(99, 102, 241, 0.06) 2px, transparent 2px),
    radial-gradient(circle at 50% 50%, rgba(168, 85, 247, 0.04) 2px, transparent 2px);
  background-size: 60px 60px, 80px 80px, 100px 100px;
  background-position: 0 0, 30px 30px, 50px 50px;
  z-index: -1;
  opacity: 0.7;
  animation: pattern-drift 20s linear infinite;
}

@keyframes pattern-drift {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

/* Tech Grid Background */
.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px),
    linear-gradient(rgba(99, 102, 241, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.02) 1px, transparent 1px);
  background-size: 100px 100px, 100px 100px, 20px 20px, 20px 20px;
  z-index: -1;
}

/* Hexagon Pattern */
.hexagon-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 50% 50%, rgba(0, 212, 255, 0.05) 30%, transparent 30%);
  background-size: 120px 104px;
  background-position: 0 0, 60px 52px;
  z-index: -1;
  animation: hexagon-pulse 8s ease-in-out infinite;
}

@keyframes hexagon-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.section-header {
  text-align: center;
  max-width: 80rem;
  margin: 0 auto 6rem;
}

.section-subtitle {
  display: inline-block;
  font-family: var(--font-heading);
  font-size: 1.6rem;
  text-transform: uppercase;
  letter-spacing: 0.2em;
  color: var(--accent-color);
  margin-bottom: 1.5rem;
  position: relative;
}

.section-subtitle::after {
  content: "";
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 5rem;
  height: 2px;
  background: var(--accent-color);
}

.section-title {
  margin-bottom: 2rem;
}

.section-description {
  font-size: 1.8rem;
  color: var(--text-secondary);
}

/* Modern Services Section */
.services-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(32rem, 1fr));
  gap: 3rem;
  margin-bottom: 4rem;
}

.service-card {
  position: relative;
  background: var(--surface-card);
  backdrop-filter: blur(20px);
  border-radius: var(--border-radius-lg);
  padding: 3.5rem;
  transition: all var(--animation-medium) var(--animation-bounce);
  z-index: 1;
  overflow: hidden;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-glass);
  opacity: 0;
  transition: opacity var(--animation-medium) ease;
  z-index: -1;
}

.service-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: var(--shadow-lg), var(--shadow-glow);
  border-color: var(--accent-color);
}

.service-card:hover::before {
  opacity: 1;
}

.service-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-neural);
  opacity: 0;
  z-index: -2;
  transition: opacity var(--animation-medium) ease;
}

.service-card:hover .service-background {
  opacity: 0.08;
}

.service-icon {
  width: 7rem;
  height: 7rem;
  background: var(--gradient-glass);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.8rem;
  color: var(--accent-color);
  margin-bottom: 2.5rem;
  transition: all var(--animation-medium) var(--animation-bounce);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.service-card:hover .service-icon {
  transform: scale(1.15) rotate(5deg);
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-glow);
}

.service-card h3 {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
}

.service-card p {
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

.service-features {
  margin-bottom: 2.5rem;
}

.service-features li {
  position: relative;
  padding-left: 2rem;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  color: var(--text-secondary);
}

.service-features li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--accent-color);
}

.service-link {
  display: inline-flex;
  align-items: center;
  font-family: var(--font-heading);
  font-weight: 600;
  font-size: 1.5rem;
  color: var(--accent-color);
}

.service-link::after {
  content: "→";
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.service-link:hover::after {
  transform: translateX(5px);
}

.section-cta {
  text-align: center;
  margin-top: 2rem;
}

/* About Section */
.about-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 5rem;
  align-items: center;
}

.about-image-container {
  position: relative;
}

.about-image {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  opacity: 0.2;
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: -5rem;
  position: relative;
  z-index: 2;
}

.stat-item {
  background: var(--secondary-bg);
  border-radius: 0.8rem;
  padding: 2rem;
  text-align: center;
  box-shadow: var(--shadow-md);
}

.stat-number {
  font-family: var(--font-heading);
  font-size: 3.6rem;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
}

.stat-symbol {
  font-family: var(--font-heading);
  font-size: 2.4rem;
  font-weight: 700;
  color: var(--accent-color);
}

.stat-label {
  font-size: 1.3rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
  display: block;
}

.about-text {
  padding-right: 2rem;
}

.about-features {
  margin: 3rem 0;
}

.feature-item {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.feature-icon {
  flex-shrink: 0;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: rgba(65, 105, 225, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-color);
  font-size: 1.8rem;
}

.feature-text h4 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.feature-text p {
  font-size: 1.5rem;
  margin-bottom: 0;
}

/* Process Timeline */
.process-timeline {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 80rem;
  margin: 0 auto;
  position: relative;
}

.process-timeline::before {
  content: "";
  position: absolute;
  top: 0;
  left: 4.5rem;
  width: 2px;
  height: 100%;
  background: var(--accent-color);
  opacity: 0.3;
}

.timeline-node {
  display: flex;
  gap: 3rem;
  position: relative;
  padding-left: 10rem;
}

.node-number {
  position: absolute;
  left: 0;
  top: 0;
  width: 9rem;
  height: 5rem;
  background: var(--secondary-bg);
  border-radius: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-heading);
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-color);
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease, background 0.3s ease, color 0.3s ease;
}

.timeline-node:hover .node-number {
  transform: scale(1.1);
  background: var(--accent-color);
  color: white;
}

.node-content {
  flex: 1;
  background: var(--secondary-bg);
  border-radius: 1rem;
  padding: 2rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.timeline-node:hover .node-content {
  transform: translateX(10px);
  box-shadow: var(--shadow-md);
}

.node-content h3 {
  font-size: 2.2rem;
  margin-bottom: 1rem;
}

.node-content p {
  font-size: 1.5rem;
  margin-bottom: 0;
}

/* Testimonials */
.testimonials-slider {
  position: relative;
  max-width: 80rem;
  margin: 0 auto;
}

.testimonial-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  opacity: 0;
  visibility: hidden;
  transform: translateX(50px);
  transition: opacity 0.5s ease, visibility 0.5s ease, transform 0.5s ease;
}

.testimonial-slide.active {
  position: relative;
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.testimonial-card {
  background: var(--secondary-bg);
  border-radius: 1rem;
  padding: 4rem;
  box-shadow: var(--shadow-md);
  position: relative;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: 2rem;
  left: 2rem;
  font-family: var(--font-heading);
  font-size: 12rem;
  line-height: 1;
  color: var(--accent-color);
  opacity: 0.1;
}

.testimonial-quote {
  font-size: 2rem;
  font-style: italic;
  color: var(--text-primary);
  margin-bottom: 3rem;
  position: relative;
  z-index: 1;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.author-image {
  width: 6rem;
  height: 6rem;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--accent-color);
}

.author-info h4 {
  font-size: 1.8rem;
  margin-bottom: 0.2rem;
}

.author-info p {
  font-size: 1.4rem;
  color: var(--text-secondary);
  margin-bottom: 0;
}

.testimonial-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  margin-top: 4rem;
}

.control-prev,
.control-next {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: var(--tertiary-bg);
  border: none;
  font-size: 1.8rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: background 0.3s ease, transform 0.3s ease;
}

.control-prev:hover,
.control-next:hover {
  background: var(--accent-color);
  color: white;
  transform: scale(1.1);
}

.control-indicators {
  display: flex;
  gap: 1rem;
}

.indicator {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: var(--tertiary-bg);
  cursor: pointer;
  transition: background 0.3s ease, transform 0.3s ease;
}

.indicator.active {
  background: var(--accent-color);
  transform: scale(1.2);
}
/*  */
/* Contact Page Specific Styles */
#contact-main {
  padding: 8rem 0;
}

/* Improved grid layout */
.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

/* Contact details section */
.contact-details {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

/* Map section */
.contact-map {
  position: relative;
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  height: 35rem;
  margin-bottom: 3rem;
}

.map-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(10, 10, 20, 0.2), rgba(65, 105, 225, 0.1));
  pointer-events: none;
}

.map-pin {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--accent-color);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 3rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: var(--shadow-md);
  pointer-events: none;
  animation: pulse 2s infinite;
}

.map-pin i {
  font-size: 1.8rem;
}

.map-pin span {
  font-weight: 600;
  font-size: 1.4rem;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(65, 105, 225, 0.6);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(65, 105, 225, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(65, 105, 225, 0);
  }
}

/* Contact info cards */
.contact-info-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.info-card {
  background: var(--secondary-bg);
  border-radius: 1.5rem;
  padding: 2.5rem;
  box-shadow: var(--shadow-sm);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.info-icon {
  width: 5rem;
  height: 5rem;
  background: rgba(65, 105, 225, 0.1);
  border-radius: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--accent-color);
  flex-shrink: 0;
  transition: background 0.3s ease, color 0.3s ease;
}

.info-card:hover .info-icon {
  background: var(--accent-color);
  color: white;
}

.info-content h3 {
  font-size: 1.8rem;
  margin-bottom: 0.8rem;
}

.info-content p {
  font-size: 1.4rem;
  margin-bottom: 0;
  line-height: 1.6;
}

.info-content a {
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.info-content a:hover {
  color: var(--accent-color);
}

/* Company info section */
.company-info {
  background: var(--secondary-bg);
  border-radius: 1.5rem;
  padding: 2.5rem;
  box-shadow: var(--shadow-sm);
}

.company-info h3 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
}

.company-info p {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.social-links {
  display: flex;
  gap: 1.5rem;
  margin-top: 2rem;
}

.social-link {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: rgba(65, 105, 225, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: var(--accent-color);
  transition: all 0.3s ease;
}

.social-link:hover {
  background: var(--accent-color);
  color: white;
  transform: translateY(-5px);
}

/* Contact form container */
.contact-form-container {
  background: var(--secondary-bg);
  border-radius: 1.5rem;
  padding: 3.5rem;
  box-shadow: var(--shadow-md);
  height: 100%;
}

.form-header {
  margin-bottom: 2.5rem;
  text-align: left;
}

.form-header h3 {
  font-size: 2.4rem;
  margin-bottom: 1rem;
}

.form-header p {
  font-size: 1.5rem;
  color: var(--text-secondary);
}

/* Form group styling improvements */
.contact-form .form-group {
  margin-bottom: 2rem;
}

.contact-form .form-group input,
.contact-form .form-group textarea,
.contact-form .form-group select {
  padding: 1.5rem 2rem;
  border-radius: 1rem;
  background: var(--tertiary-bg);
  border: 1px solid rgba(65, 105, 225, 0.2);
  width: 100%;
  font-size: 1.5rem;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.contact-form .form-group.error input,
.contact-form .form-group.error textarea,
.contact-form .form-group.error select {
  border-color: var(--error-color);
}

.contact-form .form-group input:focus,
.contact-form .form-group textarea:focus,
.contact-form .form-group select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(65, 105, 225, 0.2);
}

.contact-form .form-group textarea {
  min-height: 15rem;
  resize: vertical;
}

.form-submit {
  width: 100%;
  margin-top: 1.5rem;
  font-size: 1.6rem;
  padding: 1.5rem;
}

/* FAQ section improvements */
#faq {
  padding-top: 10rem;
  padding-bottom: 12rem;
}

.faq-container {
  max-width: 80rem;
  margin: 0 auto;
}

.faq-item {
  background: var(--secondary-bg);
  border-radius: 1.5rem;
  margin-bottom: 2rem;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: box-shadow 0.3s ease;
}

.faq-item:hover {
  box-shadow: var(--shadow-md);
}

.faq-question {
  padding: 2.5rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faq-question h3 {
  font-size: 1.8rem;
  margin-bottom: 0;
}

.question-icon {
  width: 3rem;
  height: 3rem;
  background: rgba(65, 105, 225, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.faq-item.active .question-icon {
  background: var(--accent-color);
  color: white;
  transform: rotate(45deg);
}

.faq-answer {
  padding: 0 2.5rem;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
  padding: 0 2.5rem 2.5rem;
  max-height: 20rem;
}

/* Hero improvements */
#hero {
  height: 60vh;
  min-height: 40rem;
}

.hero-text h1 {
  font-size: 7rem;
  margin-bottom: 2rem;
}

.hero-text p {
  font-size: 2.2rem;
  max-width: 60rem;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .contact-info-cards {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 992px) {
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 5rem;
  }
  
  .contact-form-container {
    order: -1;
  }
  
  .contact-details {
    gap: 4rem;
  }
}

@media (max-width: 768px) {
  .contact-info-cards {
    grid-template-columns: 1fr;
  }
  
  .info-card {
    padding: 2rem;
  }
  
  #hero {
    height: 50vh;
  }
  
  .hero-text h1 {
    font-size: 5rem;
  }
  
  .hero-text p {
    font-size: 1.8rem;
  }
  
  .contact-form-container {
    padding: 2.5rem;
  }
}

@media (max-width: 576px) {
  .hero-text h1 {
    font-size: 4rem;
  }
  
  .contact-map {
    height: 25rem;
  }
}
/* Footer */
footer {
  background: var(--secondary-bg);
  position: relative;
  z-index: 10;
}

.footer-main {
  padding: 6rem 0;
}

.footer-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 4rem;
}

.footer-info {
  max-width: 30rem;
}

.footer-logo {
  height: 4rem;
  margin-bottom: 2rem;
}

.footer-tagline {
  font-size: 1.5rem;
  margin-bottom: 2.5rem;
}

.footer-nav h4,
.footer-services h4,
.footer-contact h4 {
  font-size: 1.8rem;
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 1rem;
}

.footer-nav h4::after,
.footer-services h4::after,
.footer-contact h4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 5rem;
  height: 2px;
  background: var(--accent-color);
}

.footer-nav ul,
.footer-services ul {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-nav ul li a,
.footer-services ul li a {
  font-size: 1.5rem;
  color: var(--text-secondary);
  transition: color 0.3s ease, transform 0.3s ease;
  display: inline-block;
}

.footer-nav ul li a:hover,
.footer-services ul li a:hover {
  color: var(--text-primary);
  transform: translateX(5px);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.contact-item i {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background: rgba(65, 105, 225, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--accent-color);
}

.contact-item p {
  font-size: 1.5rem;
  margin-bottom: 0;
}

.footer-bottom {
  border-top: 1px solid rgba(65, 105, 225, 0.1);
  padding: 2rem 0;
}

.footer-bottom .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  font-size: 1.4rem;
  margin-bottom: 0;
}

.footer-links {
  display: flex;
  gap: 2rem;
}

.footer-links a {
  font-size: 1.4rem;
  color: var(--text-secondary);
}

/* Sound Toggle */
.sound-toggle {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  width: 4rem;
  height: 4rem;
  background: var(--secondary-bg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 100;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

.sound-toggle:hover {
  transform: scale(1.1);
}

.sound-icon {
  position: absolute;
  font-size: 1.8rem;
  color: var(--text-primary);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.sound-icon.sound-on {
  opacity: 0;
  transform: scale(0);
}

.sound-icon.sound-off {
  opacity: 1;
  transform: scale(1);
}

.sound-icon.active {
  opacity: 1;
  transform: scale(1);
}

.sound-icon.sound-off.active {
  opacity: 0;
  transform: scale(0);
}

/* Floating CTA */
.floating-cta {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 100;
}

.floating-button {
  width: 6rem;
  height: 6rem;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.4rem;
  color: white;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
  animation: pulse-cta 2s infinite;
}

.floating-button:hover {
  transform: scale(1.1);
  animation-play-state: paused;
}

@keyframes pulse-cta {
  0% {
    box-shadow: 0 0 0 0 rgba(65, 105, 225, 0.5);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(65, 105, 225, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(65, 105, 225, 0);
  }
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: -5rem;
  right: 2rem;
  width: 4rem;
  height: 4rem;
  background: var(--secondary-bg);
  border-radius: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  color: var(--text-primary);
  cursor: pointer;
  z-index: 99;
  transition: bottom 0.3s ease, background 0.3s ease;
  box-shadow: var(--shadow-md);
}

.back-to-top.visible {
  bottom: 9rem;
}

.back-to-top:hover {
  background: var(--accent-color);
  color: white;
}

/* Hero Section Responsive Improvements */
@media (max-width: 768px) {
  #hero {
    min-height: 50rem;
  }

  .hero-content {
    padding: 2rem 0;
    gap: 3rem;
  }

  .hero-text h1 {
    font-size: 4rem;
  }

  .hero-text p {
    font-size: 1.6rem;
  }

  .hero-cta {
    margin-top: 2rem;
  }

  .cta-button {
    width: 100%;
    justify-content: center;
    padding: 1rem 2rem;
  }

  .hero-highlights {
    margin-top: 1rem;
  }

  .highlight-card {
    padding: 1.5rem;
    min-width: auto;
    flex-direction: row;
    align-items: center;
  }

  .highlight-icon {
    width: 4rem;
    height: 4rem;
    font-size: 1.8rem;
  }

  .scroll-indicator {
    bottom: 2rem;
  }
}

@media (max-width: 576px) {
  .hero-text h1 {
    font-size: 3.6rem;
  }

  .hero-text p {
    font-size: 1.5rem;
  }

  .highlight-card {
    padding: 1.2rem;
  }

  .highlight-text h3 {
    font-size: 1.6rem;
  }

  .highlight-text p {
    font-size: 1.3rem;
  }

  .scroll-indicator {
    display: none;
  }
}

/* Responsive Styles */
@media (max-width: 1200px) {
  html {
    font-size: 58%;
  }

  .about-content,
  .contact-content {
    gap: 3rem;
  }

  .services-showcase {
    grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  }

  .footer-grid {
    gap: 3rem;
  }
}

@media (max-width: 992px) {
  html {
    font-size: 56%;
  }

  h1 {
    font-size: 5rem;
  }

  .about-content,
  .contact-content {
    grid-template-columns: 1fr;
  }

  .about-image-container {
    order: -1;
  }

  .footer-grid {
    grid-template-columns: 1fr 1fr;
    gap: 4rem 2rem;
  }

  .contact-content {
    display: flex;
    flex-direction: column;
    gap: 4rem;
  }

  .contact-info {
    max-width: 100%;
  }

  .contact-methods {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  html {
    font-size: 54%;
  }

  h1 {
    font-size: 4.2rem;
  }

  h2 {
    font-size: 3.6rem;
  }

  .nav-toggle {
    display: block;
  }

  nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--primary-bg);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  nav.active {
    opacity: 1;
    visibility: visible;
  }

  .nav-links {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .dropdown-wrapper {
    position: static;
    transform: none;
    opacity: 1;
    visibility: visible;
    background: transparent;
    box-shadow: none;
    padding: 0;
    margin-top: 1rem;
    width: 100%;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    border: none;
  }

  .dropdown.active .dropdown-wrapper {
    max-height: 50rem;
  }

  .dropdown-content {
    grid-template-columns: 1fr;
  }

  .dropdown-toggle {
    display: inline-block;
  }

  .nav-link-wrapper {
    width: 100%;
    justify-content: center;
  }

  .section {
    padding: 6rem 0;
  }

  .process-timeline::before {
    left: 2.5rem;
  }

  .timeline-node {
    padding-left: 5rem;
  }

  .node-number {
    width: 5rem;
    height: 5rem;
    font-size: 1.6rem;
  }

  .testimonial-card {
    padding: 3rem;
  }

  .contact-form-container {
    padding: 3rem;
  }

  .contact-methods {
    grid-template-columns: 1fr;
  }

  .faq-question h3 {
    font-size: 1.8rem;
  }

  .hero-highlights {
    flex-direction: column;
    gap: 1.5rem;
  }

  .highlight-card {
    width: 100%;
  }
}

@media (max-width: 576px) {
  html {
    font-size: 52%;
  }

  h1 {
    font-size: 3.8rem;
  }

  h2 {
    font-size: 3rem;
  }

  .container {
    padding: 0 1.5rem;
  }

  .footer-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .footer-bottom .container {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer-links {
    justify-content: center;
  }

  .services-showcase {
    grid-template-columns: 1fr;
  }

  .hero-highlights {
    flex-direction: column;
  }

  .highlight-card {
    min-width: 100%;
  }

  .about-stats {
    grid-template-columns: 1fr;
    margin-top: -3rem;
  }

  .section-header {
    margin-bottom: 4rem;
  }

  .section-title {
    font-size: 3rem;
  }

  .contact-form-container {
    padding: 2rem;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    padding: 1.2rem 1.5rem;
  }

  .form-group label {
    top: 1.2rem;
    left: 1.5rem;
  }

  .cta-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 375px) {
  html {
    font-size: 50%;
  }

  h1 {
    font-size: 3.4rem;
  }

  .header-container {
    padding: 0 1rem;
  }

  .logo-img {
    height: 3.5rem;
  }

  .theme-toggle {
    width: 3.5rem;
    height: 3.5rem;
    margin-left: 1rem;
  }

  .contact-method {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .method-icon {
    margin-right: 0;
  }
}

/* Additional Styles for About Page */
.about-story {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5rem;
  align-items: center;
}

.story-content {
  padding-right: 2rem;
}

.story-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin: 3rem 0;
}

.story-quote {
  background: var(--tertiary-bg);
  border-left: 3px solid var(--accent-color);
  padding: 2rem;
  border-radius: 0.8rem;
  margin-top: 3rem;
}

.story-quote blockquote {
  font-size: 1.8rem;
  font-style: italic;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.story-quote cite {
  font-size: 1.4rem;
  color: var(--accent-color);
}

.story-image {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

/* Team Showcase */
.team-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
  gap: 3rem;
}

.team-member-card {
  background: var(--secondary-bg);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.team-member-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.member-image {
  position: relative;
  height: 25rem;
  overflow: hidden;
}

.member-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.team-member-card:hover .member-image img {
  transform: scale(1.05);
}

.member-info {
  padding: 2rem;
}

.member-info h3 {
  font-size: 2.2rem;
  margin-bottom: 0.5rem;
}

.member-position {
  display: inline-block;
  font-size: 1.4rem;
  color: var(--accent-color);
  margin-bottom: 1.5rem;
}

.member-info p {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

.member-social {
  display: flex;
  gap: 1rem;
}

/* Values Section */
.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
  gap: 3rem;
}

.value-card {
  background: var(--secondary-bg);
  border-radius: 1rem;
  padding: 3rem;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-md);
}

.value-icon {
  width: 7rem;
  height: 7rem;
  background: rgba(65, 105, 225, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: var(--accent-color);
  margin: 0 auto 2rem;
  transition: background 0.3s ease, color 0.3s ease;
}

.value-card:hover .value-icon {
  background: var(--accent-color);
  color: white;
}

.value-card h3 {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
}

.value-card p {
  font-size: 1.5rem;
}

/* CTA Section */
#cta {
  background: var(--gradient-primary);
  padding: 8rem 0;
  text-align: center;
  margin-top: 6rem;
}

.cta-content {
  max-width: 70rem;
  margin: 0 auto;
}

.cta-content h2 {
  color: white;
  font-size: 3.6rem;
  margin-bottom: 2rem;
}

.cta-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.8rem;
  margin-bottom: 3rem;
}

#cta .cta-button {
  background: white;
  color: var(--accent-color);
}

#cta .cta-button:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-3px);
}

/* Services Page Styles */
.services-tabs {
  margin-bottom: 5rem;
}

.tabs-nav {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
  justify-content: center;
}

.tab-button {
  padding: 1.2rem 2.4rem;
  background: var(--tertiary-bg);
  border: none;
  border-radius: 3rem;
  font-family: var(--font-heading);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-button:hover {
  background: rgba(65, 105, 225, 0.1);
  color: var(--text-primary);
}

.tab-button.active {
  background: var(--accent-color);
  color: white;
}

/* Service Detail */
.service-detail {
  margin-bottom: 10rem;
  scroll-margin-top: 10rem;
}

.service-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 4rem;
}

.service-number {
  font-family: var(--font-heading);
  font-size: 5rem;
  font-weight: 700;
  color: var(--accent-color);
  opacity: 0.3;
  line-height: 1;
}

.service-icon {
  width: 7rem;
  height: 7rem;
  background: rgba(65, 105, 225, 0.1);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: var(--accent-color);
}

.service-header h2 {
  font-size: 3.6rem;
  margin-bottom: 0;
}

.service-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5rem;
  align-items: center;
}

.service-body:nth-of-type(even) {
  direction: rtl;
}

.service-body:nth-of-type(even) .service-description,
.service-body:nth-of-type(even) .service-image {
  direction: ltr;
}

.service-description p {
  font-size: 1.6rem;
  margin-bottom: 3rem;
}

.service-image {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.service-image img {
  width: 100%;
  height: auto;
  display: block;
}

/* Services Summary */
.services-summary {
  margin-top: 6rem;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
  gap: 3rem;
}

.summary-card {
  background: var(--secondary-bg);
  border-radius: 1rem;
  padding: 3rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-md);
}

.card-icon {
  width: 6rem;
  height: 6rem;
  background: rgba(65, 105, 225, 0.1);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: var(--accent-color);
  margin-bottom: 2rem;
  transition: background 0.3s ease, color 0.3s ease;
}

.summary-card:hover .card-icon {
  background: var(--accent-color);
  color: white;
}

.summary-card h3 {
  font-size: 2.2rem;
  margin-bottom: 1.5rem;
}

.summary-card p {
  font-size: 1.5rem;
  margin-bottom: 2rem;
}

.summary-list {
  margin-left: 2rem;
}

.summary-list li {
  font-size: 1.4rem;
  color: var(--text-secondary);
  margin-bottom: 0.8rem;
  position: relative;
}

.summary-list li::before {
  content: "•";
  color: var(--accent-color);
  position: absolute;
  left: -1.5rem;
}

/* Responsive Adjustments for Services and About Pages */
@media (max-width: 992px) {
  .about-story,
  .service-body {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .story-content {
    padding-right: 0;
  }

  .service-body:nth-of-type(even) {
    direction: ltr;
  }

  .service-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .service-icon {
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .story-stats {
    grid-template-columns: 1fr;
  }

  .team-showcase,
  .values-grid,
  .summary-grid {
    grid-template-columns: 1fr;
  }

  .tabs-nav {
    flex-direction: column;
    align-items: center;
  }

  .tab-button {
    width: 100%;
    max-width: 30rem;
  }
}

.page-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eaeaea;
}

.page-header h1 {
  margin-bottom: 0.5rem;
}

.privacy-policy-container,
.terms-of-service-container {
  max-width: 900px;
  margin: 3rem auto;
  padding: 0 1rem;
}

.policy-content section,
.terms-content section {
  margin-bottom: 2rem;
}

.policy-content h2,
.terms-content h2 {
  margin-bottom: 1rem;
}

.contact-info {
  list-style: none;
  padding-left: 0;
}

.contact-info li {
  margin-bottom: 0.5rem;
}