import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter, Space_Grotesk } from "next/font/google"
import "./globals.css"
import Header from "@/components/Header"
import Footer from "@/components/Footer"
import SoundToggle from "@/components/SoundToggle"
import FloatingCTA from "@/components/FloatingCTA"
import BackToTop from "@/components/BackToTop"
import CustomCursor from "@/components/CustomCursor"
import Preloader from "@/components/Preloader"
import Script from "next/script"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-body",
  display: "swap",
})

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  variable: "--font-heading",
  weight: ["300", "400", "500", "600", "700"],
  display: "swap",
})

export const metadata: Metadata = {
  title: "Qoverse - Innovative Software Solutions",
  description:
    "Qoverse provides cutting-edge software solutions including AI, web development, data analytics, and more.",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" type="image/x-icon" href="/assets/favicon.ico" />
        <Script
          src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/js/all.min.js"
          strategy="beforeInteractive"
        />
      </head>
      <body className={`${inter.variable} ${spaceGrotesk.variable}`}>
        <Preloader />
        {/* <CustomCursor /> */}
        <Header />
        <main>{children}</main>
        <Footer />
        <SoundToggle />
        {/* <FloatingCTA /> */}
        <BackToTop />

        {/* Audio Elements */}
        <audio id="background-sound" loop>
          <source src="/assets/cosmos.mp3" type="audio/mpeg" />
        </audio>
        <audio id="hover-sound">
          <source src="/assets/hover.mp3" type="audio/mpeg" />
        </audio>
        <audio id="click-sound">
          <source src="/assets/click.mp3" type="audio/mpeg" />
        </audio>
      </body>
    </html>
  )
}



import './globals.css'