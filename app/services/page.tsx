'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'

export default function ServicesPage() {
  const [activeTab, setActiveTab] = useState('all')

  const services = [
    {
      id: "generative-ai",
      number: "01",
      icon: "brain",
      title: "Generative AI",
      description: "Our Generative AI solutions leverage cutting-edge technology to create content, generate insights, and automate complex tasks. We develop custom AI models tailored to your specific business needs, enabling you to harness the transformative power of artificial intelligence.",
      features: [
        {
          title: "Custom LLM Development",
          description: "Tailored language models designed for your specific industry and use cases."
        },
        {
          title: "AI-Powered Content Creation",
          description: "Generate high-quality text, images, and other media automatically."
        },
        {
          title: "Conversational AI Systems",
          description: "Advanced chatbots and virtual assistants for customer service and support."
        }
      ],
      image: "/assets/generative-ai.jpg",
      category: "ai"
    },
    {
      id: "web-mobile",
      number: "02",
      icon: "laptop-code",
      title: "Web & Mobile Development",
      description: "We build scalable, intuitive digital platforms that enhance your brand presence and user experience. Our development team creates responsive web applications and mobile solutions that perform flawlessly across all devices, driving engagement and conversion.",
      features: [
        {
          title: "Progressive Web Apps",
          description: "Combining the best of web and mobile app functionality for optimal performance."
        },
        {
          title: "Native Mobile Applications",
          description: "Custom iOS and Android apps built for superior performance and user experience."
        },
        {
          title: "Cross-Platform Solutions",
          description: "Efficient development that works seamlessly across multiple platforms."
        }
      ],
      image: "/assets/web-mobile.jpg",
      category: "development"
    },
    {
      id: "data-management",
      number: "03",
      icon: "chart-bar",
      title: "Data & Analytics",
      description: "Transform your raw data into actionable insights with our comprehensive data management and analytics solutions. We help you collect, process, analyze, and visualize data to uncover patterns, trends, and opportunities that drive strategic decision-making.",
      features: [
        {
          title: "Big Data Processing",
          description: "Efficient handling of large, complex datasets for comprehensive analysis."
        },
        {
          title: "Predictive Analytics",
          description: "Forecast trends and outcomes to inform strategic planning and operations."
        },
        {
          title: "Interactive Dashboards",
          description: "Customized visualization tools that make complex data easy to understand."
        }
      ],
      image: "/assets/data-analytics.jpg",
      category: "data"
    },
    {
      id: "digital-commerce",
      number: "04",
      icon: "shopping-cart",
      title: "Digital Commerce",
      description: "Elevate your e-commerce presence with our comprehensive digital commerce solutions. We design and develop scalable, secure online shopping experiences that drive sales, enhance customer engagement, and streamline operations through intelligent automation.",
      features: [
        {
          title: "E-commerce Platforms",
          description: "Custom or platform-based solutions optimized for conversion and user experience."
        },
        {
          title: "Payment Integration",
          description: "Secure, seamless payment processing with multiple gateway options."
        },
        {
          title: "Inventory Management",
          description: "Automated systems to track and manage product inventory efficiently."
        }
      ],
      image: "/assets/digital-commerce.jpg",
      category: "development"
    },
    {
      id: "ai-bi",
      number: "05",
      icon: "chart-line",
      title: "AI Business Intelligence",
      description: "Our AI-powered Business Intelligence solutions transform how you analyze data and make decisions. By combining advanced artificial intelligence with robust business analytics, we provide predictive insights that help you identify opportunities, mitigate risks, and optimize operations.",
      features: [
        {
          title: "Intelligent Data Analysis",
          description: "AI-driven systems that automatically identify patterns and anomalies in your data."
        },
        {
          title: "Automated Reporting",
          description: "Custom reports generated automatically based on your specific KPIs and metrics."
        },
        {
          title: "Decision Support Systems",
          description: "AI-powered recommendations to guide strategic and operational decisions."
        }
      ],
      image: "/assets/ai-bi.jpg",
      category: "ai"
    },
    {
      id: "cloud-native",
      number: "06",
      icon: "cloud",
      title: "Cloud-Native Solutions",
      description: "Modern cloud infrastructure designed for scalability, performance, and cost-efficiency.",
      features: [
        "Cloud Migration & Modernization",
        "Microservices Architecture",
        "Serverless Applications"
      ],
      category: "development"
    },
    {
      id: "product-design",
      number: "07",
      icon: "pencil-ruler",
      title: "Product Design & UI/UX",
      description: "Human-centered design approaches that create intuitive, engaging digital experiences.",
      features: [
        "User Research & Testing",
        "Interface Design",
        "Experience Prototyping"
      ],
      category: "development"
    },
    {
      id: "custom-software",
      number: "08",
      icon: "code",
      title: "Custom Software",
      description: "Bespoke applications built specifically to address your unique business challenges.",
      features: [
        "Enterprise Applications",
        "Legacy System Modernization",
        "Integration Solutions"
      ],
      category: "development"
    },
    {
      id: "devops",
      number: "09",
      icon: "cogs",
      title: "DevOps & Automation",
      description: "Streamlined development processes that increase efficiency and reduce time-to-market.",
      features: [
        "CI/CD Pipeline Implementation",
        "Infrastructure as Code",
        "Automated Testing"
      ],
      category: "development"
    },
    {
      id: "consulting",
      number: "10",
      icon: "users-cog",
      title: "Technology Consulting",
      description: "Strategic guidance to help you navigate digital transformation and technology adoption.",
      features: [
        "Digital Strategy Development",
        "Technology Assessment",
        "Roadmap Planning"
      ],
      category: "consulting"
    }
  ]

  // Filter services based on active tab
  const getFilteredServices = () => {
    if (activeTab === 'all') return services
    return services.filter(service => service.category === activeTab)
  }

  // Add scroll animation for section headers
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate')
        }
      })
    }, observerOptions)

    const sections = document.querySelectorAll('.service-detail, .section-header')
    sections.forEach(section => {
      observer.observe(section)
    })

    return () => {
      sections.forEach(section => {
        observer.unobserve(section)
      })
    }
  }, [])

  return (
    <main>
      <section id="hero">
        <div className="parallax"></div>
        <div id="three-canvas"></div>
        <div className="hero-content container">
          <div className="hero-text">
            <h1 className="glitch-effect" data-text="Our Services">Our Services</h1>
            <p className="typewriter">Explore our comprehensive technology solutions.</p>
          </div>
          {/* <div className="scroll-indicator">
            <div className="mouse">
              <div className="mouse-wheel"></div>
            </div>
            <div className="scroll-text">Scroll Down</div>
          </div> */}
        </div>
      </section>

      <section id="services-intro" className="section">
        <div className="section-pattern"></div>
        <div className="container">
          <div className="section-header">
            <span className="section-subtitle">What We Offer</span>
            <h2 className="section-title">Our Expertise</h2>
            <p className="section-description">Unlock your potential with Qoverse's tailored, innovative software solutions.</p>
          </div>
          <div className="services-tabs">
            <div className="tabs-nav">
              <button 
                className={`tab-button ${activeTab === 'all' ? 'active' : ''}`} 
                onClick={() => setActiveTab('all')}
              >
                All Services
              </button>
              <button 
                className={`tab-button ${activeTab === 'ai' ? 'active' : ''}`} 
                onClick={() => setActiveTab('ai')}
              >
                AI & ML
              </button>
              <button 
                className={`tab-button ${activeTab === 'development' ? 'active' : ''}`} 
                onClick={() => setActiveTab('development')}
              >
                Development
              </button>
              <button 
                className={`tab-button ${activeTab === 'data' ? 'active' : ''}`} 
                onClick={() => setActiveTab('data')}
              >
                Data
              </button>
              <button 
                className={`tab-button ${activeTab === 'consulting' ? 'active' : ''}`} 
                onClick={() => setActiveTab('consulting')}
              >
                Consulting
              </button>
            </div>
          </div>
        </div>
      </section>

      <section id="services-list" className="section">
        <div className="container">
          {/* Detailed Service Sections */}
          {services.slice(0, 5).map((service, index) => (
            <div id={service.id} key={service.id} className="service-detail">
              <div className="service-header">
                <div className="service-number">{service.number}</div>
                <div className="service-icon">
                  <i className={`fas fa-${service.icon}`}></i>
                </div>
                <h2>{service.title}</h2>
              </div>
              <div className="service-body" style={index % 2 !== 0 ? {direction: 'rtl'} : {}}>
                {index % 2 !== 0 && (
                  <div className="service-image" style={{direction: 'ltr'}}>
                    <Image 
                      src={service.image ?? '/assets/default-service.jpg'} 
                      alt={`${service.title} Services`} 
                      width={600} 
                      height={400} 
                      layout="responsive"
                    />
                    <div className="image-overlay"></div>
                  </div>
                )}
                <div className="service-description" style={index % 2 !== 0 ? {direction: 'ltr'} : {}}>
                  <p>{service.description}</p>
                  <div className="service-features">
                    {service.features.map((feature, featureIndex) => (
                      <div className="feature-item" key={featureIndex}>
                        <div className="feature-icon">
                          <i className="fas fa-check-circle"></i>
                        </div>
                        <div className="feature-text">
                          <h4>{typeof feature === 'string' ? feature : feature.title}</h4>
                          <p>{typeof feature === 'string' ? '' : feature.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                {index % 2 === 0 && (
                  <div className="service-image">
                    <Image 
                      src={service.image ?? '/assets/default-service.jpg'} 
                      alt={`${service.title} Services`} 
                      width={600} 
                      height={400} 
                      layout="responsive"
                    />
                    <div className="image-overlay"></div>
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Summarized Services */}
          <div className="services-summary">
            <div className="summary-grid">
              {services.slice(5).map((service) => (
                <div id={service.id} key={service.id} className="summary-card">
                  <div className="card-icon">
                    <i className={`fas fa-${service.icon}`}></i>
                  </div>
                  <h3>{service.title}</h3>
                  <p>{service.description}</p>
                  <ul className="summary-list">
                    {service.features.map((feature, index) => (
                      <li key={index}>{typeof feature === 'string' ? feature : feature.title}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      <section id="process" className="section">
        <div className="container">
          <div className="section-header">
            <span className="section-subtitle">How We Work</span>
            <h2 className="section-title">Our Process</h2>
            <p className="section-description">We follow a structured approach to deliver exceptional solutions tailored to your specific needs.</p>
          </div>
          <div className="process-timeline">
            <div className="timeline-node">
              <div className="node-number">01</div>
              <div className="node-content">
                <h3>Discovery</h3>
                <p>We begin with a thorough understanding of your business, goals, and challenges.</p>
              </div>
            </div>
            <div className="timeline-node">
              <div className="node-number">02</div>
              <div className="node-content">
                <h3>Strategy</h3>
                <p>Developing a comprehensive roadmap to achieve your objectives effectively.</p>
              </div>
            </div>
            <div className="timeline-node">
              <div className="node-number">03</div>
              <div className="node-content">
                <h3>Design</h3>
                <p>Creating intuitive, visually appealing interfaces that enhance user experience.</p>
              </div>
            </div>
            <div className="timeline-node">
              <div className="node-number">04</div>
              <div className="node-content">
                <h3>Development</h3>
                <p>Building robust, scalable solutions using cutting-edge technologies.</p>
              </div>
            </div>
            <div className="timeline-node">
              <div className="node-number">05</div>
              <div className="node-content">
                <h3>Testing</h3>
                <p>Rigorous quality assurance to ensure flawless performance.</p>
              </div>
            </div>
            <div className="timeline-node">
              <div className="node-number">06</div>
              <div className="node-content">
                <h3>Deployment</h3>
                <p>Smooth implementation and integration with your existing systems.</p>
              </div>
            </div>
            <div className="timeline-node">
              <div className="node-number">07</div>
              <div className="node-content">
                <h3>Support</h3>
                <p>Ongoing maintenance and assistance to ensure continued success.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="cta" className="section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Elevate Your Business with Our Services?</h2>
            <p>Contact us today to discuss your project and discover how our solutions can drive your success.</p>
            <Link href="/contact" className="cta-button primary">
              <span className="button-text">Get in Touch</span>
              <span className="button-icon">→</span>
            </Link>
          </div>
        </div>
      </section>
    </main>
  )
}