"use client";
import { useEffect } from "react";
import Head from "next/head";

declare global {
  interface Window {
    audioEnabled: boolean;
  }
}
import Hero from "@/components/contact/Hero";
import ContactForm from "@/components/contact/ContactForm";
import ContactInfo from "@/components/contact/ContactInfo";
import ContactMap from "@/components/contact/ContactMap";
import FAQ from "@/components/contact/FAQ";
import { initAnimations } from "@/lib/animations";

export default function Contact() {
  useEffect(() => {
    // Initialize animations after component mounts
    const timer = setTimeout(() => {
      initAnimations();
    }, 1000);
    
    // Add sound elements to the document if they don't exist
    if (typeof window !== 'undefined') {
      if (!document.getElementById('background-sound')) {
        const backgroundSound = document.createElement('audio');
        backgroundSound.id = 'background-sound';
        backgroundSound.loop = true;
        const source = document.createElement('source');
        source.src = '/assets/cosmos.mp3';
        source.type = 'audio/mpeg';
        backgroundSound.appendChild(source);
        document.body.appendChild(backgroundSound);
      }
      
      if (!document.getElementById('hover-sound')) {
        const hoverSound = document.createElement('audio');
        hoverSound.id = 'hover-sound';
        const source = document.createElement('source');
        source.src = '/assets/hover.mp3';
        source.type = 'audio/mpeg';
        hoverSound.appendChild(source);
        document.body.appendChild(hoverSound);
      }
      
      if (!document.getElementById('click-sound')) {
        const clickSound = document.createElement('audio');
        clickSound.id = 'click-sound';
        const source = document.createElement('source');
        source.src = '/assets/click.mp3';
        source.type = 'audio/mpeg';
        clickSound.appendChild(source);
        document.body.appendChild(clickSound);
      }
      
      // Set global audio state
      window.audioEnabled = false;
    }
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <Head>
        <title>Qoverse - Contact Us</title>
        <meta name="description" content="Contact Qoverse for all your technology needs. Get in touch with our team to discuss your project and find tailored solutions." />
      </Head>
      
      {/* Custom cursor elements */}
      {/* <div className="cursor-dot"></div> */}
      {/* <div className="cursor-outline"></div> */}
      
      <Hero />
      
      <section id="contact-main" className="section">
        <div className="section-pattern"></div>
        <div className="container">
          <div className="section-header">
            <span className="section-subtitle">Get In Touch</span>
            <h2 className="section-title">Let's Start a Conversation</h2>
            <p className="section-description">
              We're here to answer your questions and discuss how we can help with your technology needs.
            </p>
          </div>
          <div className="contact-grid">
            <ContactForm />
            <div className="contact-details">
              <ContactMap />
              <ContactInfo />
            </div>
          </div>
        </div>
      </section>
      
      <FAQ />
      
      {/* Sound Toggle */}
      <div className="sound-toggle" onClick={() => {
        if (typeof window !== 'undefined') {
          const bgSound = document.getElementById('background-sound') as HTMLAudioElement;
          const soundOn = document.querySelector('.sound-icon.sound-on');
          const soundOff = document.querySelector('.sound-icon.sound-off');
          
          if (bgSound && soundOn && soundOff) {
            window.audioEnabled = !window.audioEnabled;
            
            if (window.audioEnabled) {
              bgSound.play().catch(e => {});
              soundOn.classList.add('active');
              soundOff.classList.remove('active');
            } else {
              bgSound.pause();
              soundOn.classList.remove('active');
              soundOff.classList.add('active');
            }
          }
        }
      }}>
        <div className="sound-icon sound-on">
          <i className="fas fa-volume-up"></i>
        </div>
        <div className="sound-icon sound-off active">
          <i className="fas fa-volume-mute"></i>
        </div>
      </div>
      
      {/* Back to Top Button */}
      <div className="back-to-top" onClick={() => {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }}>
        <i className="fas fa-chevron-up"></i>
      </div>
    </>
  );
}